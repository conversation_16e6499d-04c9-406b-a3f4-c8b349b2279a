<?php
/* Smarty version 4.5.5, created on 2025-07-30 15:34:42
  from '/www/wwwroot/www.95dir.com/themes/default/siteinfo.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6889cb12c5ef84_79880290',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '59c7dcd9c2af255af95a475f17b212ef21eff958' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/siteinfo.html',
      1 => 1753784210,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:script.html' => 1,
    'file:topbar.html' => 1,
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6889cb12c5ef84_79880290 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
<head>
<title><?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
 - <?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="robots" content="index,follow" />
<meta name="author" content="<?php echo $_smarty_tpl->tpl_vars['options']->value['site_name'];?>
" />
<meta name="format-detection" content="telephone=no" />
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="apple-mobile-web-app-title" content="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
 - <?php echo $_smarty_tpl->tpl_vars['options']->value['site_name'];?>
" />
<meta name="theme-color" content="#007bff" />
<link rel="canonical" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=siteinfo&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
" />
<link rel="alternate" media="only screen and (max-width: 640px)" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=siteinfo&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/svg-fix.css" rel="stylesheet" type="text/css" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/css/logo-preview.css" rel="stylesheet" type="text/css" />

<!-- SEO优化 - 结构化数据：网站详情 -->
<?php echo '<script'; ?>
 type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
",
    "url": "http://<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
",
    "description": "<?php echo strtr((string)$_smarty_tpl->tpl_vars['web']->value['web_intro'], array("\\" => "\\\\", "'" => "\\'", "\"" => "\\\"", "\r" => "\\r", 
                       "\n" => "\\n", "</" => "<\/", "<!--" => "<\!--", "<s" => "<\s", "<S" => "<\S",
                       "`" => "\\`", "\${" => "\\\$\{"));?>
",
    "inLanguage": "zh-CN",
    "datePublished": "<?php echo $_smarty_tpl->tpl_vars['web']->value['web_ctime'];?>
",
    "dateModified": "<?php echo $_smarty_tpl->tpl_vars['web']->value['web_utime'];?>
",
    "publisher": {
        "@type": "Organization",
        "name": "<?php echo $_smarty_tpl->tpl_vars['options']->value['site_name'];?>
",
        "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
"
    },
    "mainEntity": {
        "@type": "Thing",
        "name": "<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
",
        "url": "http://<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
",
        "description": "<?php echo strtr((string)$_smarty_tpl->tpl_vars['web']->value['web_intro'], array("\\" => "\\\\", "'" => "\\'", "\"" => "\\\"", "\r" => "\\r", 
                       "\n" => "\\n", "</" => "<\/", "<!--" => "<\!--", "<s" => "<\s", "<S" => "<\S",
                       "`" => "\\`", "\${" => "\\\$\{"));?>
"
    }
}
<?php echo '</script'; ?>
>

<!-- SEO优化 - 结构化数据：面包屑导航 -->
<?php echo '<script'; ?>
 type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "首页",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
"
        },
        {
            "@type": "ListItem",
            "position": 2,
            "name": "网站目录",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir"
        },
        {
            "@type": "ListItem",
            "position": 3,
            "name": "<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=webdir&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
"
        },
        {
            "@type": "ListItem",
            "position": 4,
            "name": "<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=siteinfo&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"
        }
    ]
}
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender("file:script.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/svg-fix.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/js/logo-optimizer.js"><?php echo '</script'; ?>
>
</head>

<body>
<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<div id="wrapper">
	<?php $_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="siteinfo">
            	<h1 class="wtitle">
    <span style="float: right; margin-top: 5px;">  <!-- 浮动到右侧，并添加少量外边距调整布局 -->
        <a href="/go.php?url=http://<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
)" class="access-button">
            访问站点
        </a>
    </span>
    <a href="/go.php?url=http://<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
)" title="访问<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
官方网站" rel="nofollow"><em style="color: #f60;"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
-官网入口</em></a>  <!-- 这是原有的内容，我假设它应该保留在左侧 -->
</h1>
					    <ul class="wdata" itemscope itemtype="https://schema.org/WebSite">
    <li class="line"><em><img src="module/dr_badge.php?domain=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" alt="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
域名权威度DR指数" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
网站DR评分"></em>DR指数</li>
    <li class="line"><em style="color: #f00;" itemprop="interactionStatistic" itemscope itemtype="https://schema.org/InteractionCounter"><meta itemprop="interactionType" content="https://schema.org/ViewAction"><span itemprop="userInteractionCount"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_views'];?>
</span></em>人气指数</li>
    <li class="line"><em style="color: #083;" title="百度搜索引擎收录页面数量"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_grank'];?>
</em>百度收录量</li>
    <li class="line"><em style="color: #083;" title="必应搜索引擎收录页面数量"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_brank'];?>
</em>必应收录量</li>
    <li class="line"><em style="color: #083;" title="360搜索引擎收录页面数量"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_srank'];?>
</em>360收录量</li>
    <li class="line"><em style="color: #083;" title="搜狗搜索引擎收录页面数量"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_arank'];?>
</em>搜狗收录量</li>
    <li class="line"><em id="website-status" class="status-checking" title="网站当前访问状态检测">检测中...</em>网站状态</li>
    <li class="line"><em id="response-time" class="status-checking" title="网站响应速度检测">检测中...</em>响应时间</li>
    <li class="line"><em title="从本站点击进入该网站的次数"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_instat'];?>
</em>入站次数</li>
    <li class="line"><em title="从该网站点击进入本站的次数"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_outstat'];?>
</em>出站次数</li>
    <li class="line"><em itemprop="datePublished" title="网站被收录到本目录的日期"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_ctime'];?>
</em>收录日期</li>
    <li class="line"><em style="color: #f60;" itemprop="dateModified" title="网站信息最后更新日期"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_utime'];?>
</em>更新日期</li>
</ul>

				<div class="clearfix params">
					<a href="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_links'];?>
" target="_blank" title="查看<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
网站截图"><img src="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_pic'];?>
" width="130" height="110" alt="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
网站截图预览" class="wthumb" itemprop="image" /></a>
					<ul class="siteitem" itemscope itemtype="https://schema.org/WebSite">
						<li>
    <strong>网站地址：</strong>
    <!-- 目标网址：一开始就隐藏 -->
<a id="website-link"
   href="/go.php?url=http://<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
"
   target="_blank"
   class="visit"
   onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
)"
   style="display:none;">            <!-- &larr; 新增 display:none -->
  <font color="#008000"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
</font>
</a>

<!-- 占位提示：初始为“检测中...” -->
<span id="website-link-placeholder"
      style="color:#666;margin-left:4px;">链接检测中…</span>

    <!-- 网站认领按钮 -->
    <a href="/member/?mod=claim" target="_blank">
        <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#08F;border-radius:1em;float:right;width:80px;">
            网站认领
        </span>
    </a>

    <!-- 违规举报按钮（新增） -->
    <a href="https://www.95dir.com/feedback/" target="_blank">
        <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#F44336;border-radius:1em;float:right;width:80px;margin-left:6px;">
            违规举报
        </span>
    </a>
</li>
            			<li><strong>服务器IP：</strong><span itemprop="serverIP"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_ip'];?>
</span></li>
                        <li><strong>网站描述：</strong><span style="line-height: 23px;" itemprop="description"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_intro'];?>
</span></li>
                        <li><strong>综合权重：</strong><span><img src="https://baidurank.aizhan.com/api/br?domain=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&style=images" alt="百度移动权重" /></span>
<span><img src="https://baidurank.aizhan.com/api/mbr?domain=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&style=images" alt="百度权重" /></span>
<span><img src="https://sogourank.aizhan.com/api/br?domain=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&style=images" alt="搜狗权重" /></span>
<span><img src="https://sorank.aizhan.com/api/br?domain=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&style=images" alt="360权重" /></span>
<span><img src="https://smrank.aizhan.com/api/br?domain=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&style=images" alt="神马权重" /></span>
<span><img src="https://bingrank.aizhan.com/api/br?domain=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&style=images" alt="必应权重" /></span>
<span><img src="https://toutiaorank.aizhan.com/api/br?domain=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&style=images" alt="头条权重" /></span></li>
                        <li><strong>备案信息：</strong><?php echo '<script'; ?>
 type="text/javascript" src="https://icp.aizhan.com/geticp/?host=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&amp;style=text" charset="utf-8"><?php echo '</script'; ?>
></li>
                        <li><strong>联系站长：</strong><a href="http://wpa.qq.com/msgrd?v=3&amp;uin=<?php echo $_smarty_tpl->tpl_vars['user']->value['user_qq'];?>
&amp;site=<?php echo $_smarty_tpl->tpl_vars['user']->value['nick_name'];?>
&amp;menu=yes" target="_blank"><img border="0" alt="点击这里给我发消息" src="http://wpa.qq.com/pa?p=2:<?php echo $_smarty_tpl->tpl_vars['user']->value['user_qq'];?>
:41"></a></li>
                        <li><strong>TAG标签：</strong><?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['web_tags']->value, 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?><a href="<?php echo $_smarty_tpl->tpl_vars['item']->value['tag_link'];?>
" title="查看更多<?php echo $_smarty_tpl->tpl_vars['item']->value['tag_name'];?>
相关网站" itemprop="keywords"><?php echo $_smarty_tpl->tpl_vars['item']->value['tag_name'];?>
</a>　<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?></li>
                        <li><?php echo get_adcode(1);?>
</li>
                        <li><strong>相关查询：</strong> <a rel="external nofollow" href="https://www.aizhan.com/cha/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">SEO综合查询</a> |
    <a rel="external nofollow" href="https://pr.aizhan.com/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
/" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">Google PageRank</a> |
    <a rel="external nofollow" href="https://pr.aizhan.com/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
/" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">Sogou Rank</a> |
    <a rel="external nofollow" href="https://rank.aizhan.com/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">网站权重查询</a> |
    <a rel="external nofollow" href="http://www.baidu.com/s?wd=site%3A<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&amp;cl=3" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">百度查询</a> |
    <a rel="external nofollow" href="http://www.so.com/s?q=site:<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">360搜索查询</a> |
    <a rel="external nofollow" href="http://www.soso.com/q?w=site%3A<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&amp;sc=web&amp;ch=w.ptl&amp;lr=chs" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">SOSO查询</a> |
    <a rel="external nofollow" href="http://www.sogou.com/web?query=site%3A<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">搜狗查询</a> |
    <a rel="external nofollow" href="http://cn.bing.com/search?q=site%3A<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
&amp;go=&amp;form=QBLH" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">必应查询</a> |
    <a rel="external nofollow" href="http://search.aol.com/aol/search?s_chn=prt_ct18&amp;enabled_terms=&amp;s_it=comsearch&amp;q=site%3A<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">AOL搜索</a> |
    <a rel="external nofollow" href="http://www.google.com.hk/search?hl=zh-CN&amp;q=site%3A<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">谷歌查询</a> |
    <a rel="external nofollow" href="https://icp.aizhan.com/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
/" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">网站备案查询</a> |
    <a rel="external nofollow" href="https://baidurank.aizhan.com/baidu/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
/" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">百度权重查询</a> |
    <a rel="external nofollow" href="https://linkche.aizhan.com/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
/" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">友情链接检查</a> |
    <a rel="external nofollow" href="https://ahrefs.com/website-authority-checker/?input=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">域名DR查询</a> |
    <a rel="external nofollow" href="https://zhenzhan.baidu.com/#/mnt/detail?kw=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
">百度网站检测</a></li>
<li><strong>本页地址：</strong><a href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=siteinfo&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"><?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=siteinfo&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
</a></li>
					</ul>
				</div>
            </div>
            <div class="blank10"></div>

            <!-- SEO内链优化：相关导航链接 -->
            <div class="seo-internal-links" style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="margin: 0 0 12px 0; font-size: 16px; color: #333; font-weight: bold;">
                    <i class="fas fa-link" style="color: #007bff; margin-right: 8px;"></i>相关导航
                </h4>
                <div style="line-height: 2.2; font-size: 14px;">
                    <a href="?mod=webdir&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
" title="浏览更多<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
网站" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-folder" style="margin-right: 5px;"></i>更多<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
网站
                    </a>
                    <a href="?mod=category" title="网站分类目录" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-sitemap" style="margin-right: 5px;"></i>网站分类
                    </a>
                    <a href="?mod=update" title="最新收录网站" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-clock" style="margin-right: 5px;"></i>最新收录
                    </a>
                    <a href="?mod=top" title="热门网站排行榜" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-trophy" style="margin-right: 5px;"></i>热门排行
                    </a>
                    <?php if ($_smarty_tpl->tpl_vars['web']->value['web_ispay']) {?>
                    <a href="?mod=vip_list" title="VIP网站列表" style="color: #ffd700; text-decoration: none; margin-right: 15px;">
                        <i class="fas fa-crown" style="margin-right: 5px;"></i>VIP网站
                    </a>
                    <?php }?>
                    <a href="?mod=article" title="站长资讯" style="color: #007bff; text-decoration: none;">
                        <i class="fas fa-newspaper" style="margin-right: 5px;"></i>站长资讯
                    </a>
                </div>
            </div>

            <div class="web_ai_intro">关于【<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
】的详细介绍</div>
            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
        	    <span><img src="https://cdn.iocdn.cc/mshots/v1/<?php echo $_smarty_tpl->tpl_vars['web']->value['web_furl'];?>
" width="100%" height="50%" alt="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
网站首页截图" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
网站首页预览" itemprop="screenshot" loading="lazy" /></span>
        	    <div class="blank10"></div>
				<?php echo $_smarty_tpl->tpl_vars['web']->value['web_ai_intro'];?>

			</div>

			<div class="blank10"></div>
            <div class="web_ai_intro">注意事项：凡违反中国国家法律法规的网站，95分类目录一律不给予收录。</div>
            <div class="blank10"></div>
			<div id="relsite" class="clearfix">
<p><a href="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_links'];?>
" title="访问<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
官方网站"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</a>于<?php echo $_smarty_tpl->tpl_vars['web']->value['web_ctime'];?>
被<strong>95目录网</strong>收录在<a href="?mod=webdir&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
" title="<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
网站分类目录"><?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
分类目录</a>中。该网站相关信息来自互联网或网友推荐分享，我们致力于为用户提供优质的<strong>网站收录服务</strong>和<strong>网站推荐</strong>。</p>

<p style="margin-top: 15px; padding: 10px; background: #fff3cd; border-left: 4px solid #ffc107; font-size: 14px;">
<strong>收录说明：</strong>95目录网作为专业的<strong>网站分类目录平台</strong>，严格审核每个提交的网站。由于网站内容具有动态性，我们无法保证所收录网站内容的实时准确性。用户在访问<a href="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_links'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
</a>时，请谨慎判断网站内容的真实性和合法性。如发现网址失效、内容违规等问题，请及时<a href="?mod=feedback" title="意见反馈">联系我们</a>处理。
</p>

<p style="margin-top: 15px; font-size: 13px; color: #666; line-height: 1.6;">
<strong>相关标签：</strong>
<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['web_tags']->value, 'tag', false, NULL, 'tags', array (
));
$_smarty_tpl->tpl_vars['tag']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['tag']->value) {
$_smarty_tpl->tpl_vars['tag']->do_else = false;
?>
<a href="<?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_link'];?>
" title="查看更多<?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_name'];?>
相关网站" style="color: #007bff; margin-right: 10px;"><?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_name'];?>
</a>
<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
<a href="?mod=webdir&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
" title="<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
网站大全" style="color: #007bff; margin-right: 10px;"><?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
网站</a>
<a href="?mod=webdir" title="网站目录" style="color: #007bff; margin-right: 10px;">网站目录</a>
<a href="?mod=update" title="最新收录网站" style="color: #007bff;">最新收录</a>
</p>
</div>



<!-- 上一站下一站导航 -->
<div class="website-navigation" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
	<div style="display: flex; justify-content: space-between; align-items: center;">
		<div class="prev-website" style="flex: 1; text-align: left;">
			<?php if ($_smarty_tpl->tpl_vars['prev_website']->value) {?>
				<a href="<?php echo $_smarty_tpl->tpl_vars['prev_website']->value['web_link'];?>
" target="_blank" style="color: #007bff; text-decoration: none; font-size: 14px;"
				   onmouseover="this.style.color='#0056b3'" onmouseout="this.style.color='#007bff'">
					<i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
					上一站：<?php echo $_smarty_tpl->tpl_vars['prev_website']->value['web_name'];?>

				</a>
			<?php } else { ?>
				<span style="color: #6c757d; font-size: 14px;">
					<i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
					上一站：暂无
				</span>
			<?php }?>
		</div>

		<div class="navigation-center" style="flex: 0 0 auto; margin: 0 20px;">
			<span style="color: #6c757d; font-size: 12px;">网站导航</span>
		</div>

		<div class="next-website" style="flex: 1; text-align: right;">
			<?php if ($_smarty_tpl->tpl_vars['next_website']->value) {?>
				<a href="<?php echo $_smarty_tpl->tpl_vars['next_website']->value['web_link'];?>
" target="_blank" style="color: #007bff; text-decoration: none; font-size: 14px;"
				   onmouseover="this.style.color='#0056b3'" onmouseout="this.style.color='#007bff'">
					下一站：<?php echo $_smarty_tpl->tpl_vars['next_website']->value['web_name'];?>

					<i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
				</a>
			<?php } else { ?>
				<span style="color: #6c757d; font-size: 14px;">
					下一站：暂无
					<i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
				</span>
			<?php }?>
		</div>
	</div>
</div>
<div class="text-xs text-muted"><div><span>©</span> 版权声明</div><div class="posts-copyright"><div><br><fieldset style="border:1px dashed #008CFF;padding:10px;border-radius:8px;line-height: 2em;color: #6D6D6D"><legend align="center" style="color:#FFFFFF;width:200px;text-align:center;background-color:#008CFF;font-size: 14px;border-radius: 5px">95分类目录 - 版权声明</legend>1、本主题所有言论和图片纯属会员个人意见，与本站立场无关。<br> 2、本站所有主题由该文章作者发表，该文章作者与<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>享有文章相关版权。<br> 3、其他单位或个人使用、转载或引用本文时必须同时征得该文章作者和<a href="https://www.95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>的同意。<br> 4、文章作者须承担一切因本文发表而直接或间接导致的民事或刑事法律责任。<br> 5、本帖部分内容转载自其它媒体，但并不代表本站赞同其观点和对其真实性负责。<br> 6、如本帖侵犯到任何版权问题，请立即告知本站，本站将及时予与删除并致以最深的歉意。<br> 7、<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>管理员有权不事先通知发贴者而删除本文。</fieldset><br></div></div></div>



<div class="blank10"></div>


				    <!-- 打赏按钮 -->
				    <div class="donate-button-container">
<button onclick="showDonatePopup()">我要上推荐</button>
<div class="fenxiang">
    <div class="social-share">
    </div>
    </div>
</div>
<!-- 打赏弹窗 -->
<div id="donate-popup" style="display:none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
<div class="donate-popup-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 500px; width: 90%;">
<span class="close" onclick="closeDonatePopup()" style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #999;">&times;</span>
<h3 style="color: #4A90E2; font-family: 'Arial', sans-serif; font-size: 22px; text-align: center; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); margin-top: 0;">
    推荐服务价格表
</h3>
<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <p style="margin: 0; line-height: 1.6; text-align: center;">
        <strong style="color: #28a745;">10元上推荐位</strong> - 首页推荐展示<br>
        <strong style="color: #E94E77;">VIP直链席位30元/每年</strong> - 顶部VIP位置<br>
        <strong style="color: #ff6600;">5元快审服务</strong> - 1-3个工作日审核
    </p>
</div>
<p style="text-align: center; margin: 15px 0; color: #666;">
    备注格式：<strong style="color: #F39C12;">推荐/vip/快审+网址</strong>
</p>
<div class="donate-qr-codes" style="display: flex; justify-content: space-around; margin: 20px 0;">
<div style="text-align: center;">
<h4>微信支付</h4>
<img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206265.png" alt="95目录网微信支付二维码 - 网站收录付费服务" title="微信扫码支付网站收录费用" style="width: 150px; height: 150px;" loading="lazy">
</div>
<div style="text-align: center;">
<h4>支付宝支付</h4>
<img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206984.png" alt="95目录网支付宝支付二维码 - 网站收录付费服务" title="支付宝扫码支付网站收录费用" style="width: 150px; height: 150px;" loading="lazy">
</div>
</div>
<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
    <h4 style="margin-top: 0; color: #333;">服务说明：</h4>
    <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
        <li>推荐位：展示在首页推荐区域</li>
        <li>VIP位：展示在顶部VIP推广区</li>
        <li>快审：1-3个工作日完成审核</li>
        <li>付款后请联系客服提供网站信息</li>
    </ul>
</div>
</div>
</div>


            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
            	<h2>相关站点</h2>
               	<ul class="rellist" itemscope itemtype="https://schema.org/ItemList">
              		<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['related_website']->value, 'rel');
$_smarty_tpl->tpl_vars['rel']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->do_else = false;
?>
               		<li itemprop="itemListElement" itemscope itemtype="https://schema.org/WebSite"><a href="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_link'];?>
" title="访问<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
网站" itemprop="url"><img src="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
网站截图" itemprop="image" /><strong itemprop="name"><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</strong></a></li>
               		<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
              	</ul>
            </div>

            <div class="blank10"></div>
            <div id="relsite" class="clearfix">
            	<h2>本类排行榜</h2>
               	<ul class="rellist" itemscope itemtype="https://schema.org/ItemList">
              		<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites($_smarty_tpl->tpl_vars['web']->value['cate_id'],10,false,false,'views'), 'hot', false, NULL, 'hot_website', array (
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
?>
            <li itemprop="itemListElement" itemscope itemtype="https://schema.org/WebSite"><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="访问<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
网站" itemprop="url"><img src="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
网站截图" itemprop="image" /><strong itemprop="name"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</strong></a></li>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
              	</ul>
            </div>
        </div>
        <div id="mainbox-right">
        	<!--<div class="ad250x250"><?php echo get_adcode(7);?>
</div>
            <div class="blank10"></div>-->

            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,'views'), 'quick');
$_smarty_tpl->tpl_vars['quick']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->do_else = false;
?>
                   	<li><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
网站logo - VIP优质网站推荐" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
网站截图预览" loading="lazy" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a></strong><p><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_url'];?>
</a></address></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>

            <div class="blank10"></div>

            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_articles(0,10), 'art');
$_smarty_tpl->tpl_vars['art']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['art']->value) {
$_smarty_tpl->tpl_vars['art']->do_else = false;
?>
                	<li>[<em><a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['cate_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['art']->value['cate_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['cate_name'];?>
</a></em>]<a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
</a></li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>


            <div class="blank10"></div>
            <div id="bestart">
                <h3>最新收录</h3>
            <ul class="artlist_b">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,8), 'new');
$_smarty_tpl->tpl_vars['new']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->do_else = false;
?>
					<li data-number="<?php echo $_smarty_tpl->tpl_vars['idx']->value+1;?>
">[<em><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['cate_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['cate_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['cate_name'];?>
</a></em>]<a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a><span><?php echo $_smarty_tpl->tpl_vars['new']->value['web_ctime'];?>
</span></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>


            <div class="blank10"></div>
            <div id="bestweb">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,5,false,true), 'best');
$_smarty_tpl->tpl_vars['best']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['best']->value) {
$_smarty_tpl->tpl_vars['best']->do_else = false;
?>
                   	<li><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
网站logo - 推荐优质网站" title="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
网站截图预览" loading="lazy" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
</a></strong><p><?php echo $_smarty_tpl->tpl_vars['best']->value['web_intro'];?>
</p><address><a href="/go.php?url=http://<?php echo $_smarty_tpl->tpl_vars['best']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['best']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_url'];?>
</a></address></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        </div>
    </div>
    <?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</div>

<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
?mod=getdata&type=ip&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
?mod=getdata&type=grank&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
?mod=getdata&type=brank&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
?mod=getdata&type=srank&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
?mod=getdata&type=arank&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
?mod=getdata&type=clink&wid=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
>
const checkWebsiteStatus = async () => {
  const statusEl = document.getElementById('website-status');
  const timeEl   = document.getElementById('response-time');
  const linkEl   = document.getElementById('website-link');
  const phEl     = document.getElementById('website-link-placeholder');
  const target   = '<?php echo $_smarty_tpl->tpl_vars['web']->value['web_furl'];?>
';

  /* —— 1. 开始检测 —— */
  if (linkEl) linkEl.style.display = 'none';
  if (phEl) {
    phEl.textContent = '链接检测中…';
    phEl.style.color = '#666';
    phEl.style.display = 'inline';
  }
  statusEl.textContent = '检测中...';
  statusEl.style.color = '#666';
  timeEl.textContent   = '检测中...';

  try {
    const res  = await fetch(`/module/status_check.php?url=${encodeURIComponent(target)}`);
    const data = await res.json();
    if (data.error) throw new Error(data.error);

    /* —— 2. 解析状态码 —— */
    const map = {
      200: ['正常200', '#083'],
      301: ['跳转301', '#083'],
      302: ['跳转302', '#083'],
      403: ['禁止访问403',   '#f00'],
      404: ['未找到404',     '#f00'],
      500: ['服务器错误500', '#f00'],
      503: ['服务不可用503', '#f00']
    };
    const [txt, clr] = map[data.status] || [`异常(${data.status})`, '#f00'];

    statusEl.textContent = txt;
    statusEl.style.color = clr;
    timeEl.textContent   = `${data.response_time}ms`;
    timeEl.style.color   = data.response_time > 3000 ? '#f60' : '#666';

    /* —— 3. 根据状态码显示/隐藏链接 —— */
    const hideCodes = [403, 404, 500, 503];
    if (hideCodes.includes(data.status)) {
      // 异常 &rarr; 隐藏链接，提示对应中文文案
      if (linkEl) linkEl.style.display = 'none';
      if (phEl) {
        phEl.textContent = txt;   // 直接用中文文案
        phEl.style.color = '#f00';
        phEl.style.display = 'inline';
      }
    } else {
      // 正常 &rarr; 显示链接，隐藏提示
      if (linkEl) linkEl.style.display = '';
      if (phEl) phEl.style.display = 'none';
    }

  } catch (e) {
    /* —— 4. 检测失败 —— */
    if (linkEl) linkEl.style.display = 'none';
    if (phEl) {
      phEl.textContent = '链接检测失败';
      phEl.style.color = '#f00';
      phEl.style.display = 'inline';
    }
    statusEl.textContent = '链接检测失败';
    statusEl.style.color = '#f00';
    timeEl.textContent   = '';
  }
};

/* 页面加载即检测，并每 5 分钟重检 */
checkWebsiteStatus();
setInterval(checkWebsiteStatus, 300000);
<?php echo '</script'; ?>
>
</body>
</html><?php }
}
