# ==========================================
# 95分类目录 - 宝塔伪静态规则
# ==========================================

# 首页、分类浏览、数据归档、最近更新、排行榜、意见反馈等模块
location ~ ^/(index|webdir|weblink|article|category|arccate|update|archives|top|ipinfo|vip|domain|wait|addurl|feedback|link|diypage|rssfeed|sitemap)/?$ {
    try_files $uri /index.php?mod=$1;
}

# 网站分类页面 (website模块)
location ~ ^/website/(.+)-(\d+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=website&cid=$2&page=$3;
}
location ~ ^/website/(.+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=website&cid=$2;
}

# 文章分类页面 (arccate模块)
location ~ ^/arccate/(.+)-(\d+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=arccate&cid=$2&page=$3;
}
location ~ ^/arccate/(.+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=arccate&cid=$2;
}
location ~ ^/arccate/(.+)-(\d+)-(\d+)/?$ {
    try_files $uri /index.php?mod=arccate&cid=$2&page=$3;
}
location ~ ^/arccate/(.+)-(\d+)/?$ {
    try_files $uri /index.php?mod=arccate&cid=$2;
}

# VIP页面
location ~ ^/vip/(\d+)\.html$ {
    try_files $uri /index.php?mod=vip&page=$1;
}

# 等待审核页面
location ~ ^/wait/(\d+)\.html$ {
    try_files $uri /index.php?mod=wait&page=$1;
}

# 域名页面
location ~ ^/domain/(\d+)\.html$ {
    try_files $uri /index.php?mod=domain&page=$1;
}

# 添加网址页面
location ~ ^/addurl/(add)\.html$ {
    try_files $uri /index.php?mod=addurl&act=$1;
}

# 最近更新
location ~ ^/update/(\d+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=update&days=$1&page=$2;
}
location ~ ^/update/(\d+)\.html$ {
    try_files $uri /index.php?mod=update&days=$1;
}

# 数据归档
location ~ ^/archives/(\d+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=archives&date=$1&page=$2;
}
location ~ ^/archives/(\d+)\.html$ {
    try_files $uri /index.php?mod=archives&date=$1;
}

# 站内搜索
location ~ ^/search/(name|url|tags|intro)-(.+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=search&type=$1&query=$2&page=$3;
}
location ~ ^/search/(name|url|tags|intro)-(.+)\.html$ {
    try_files $uri /index.php?mod=search&type=$1&query=$2;
}

# 网站详细页面
location ~ ^/website/(\d+)\.html$ {
    try_files $uri /index.php?mod=siteinfo&wid=$1;
}

# 文章详细页面
location ~ ^/article/(.+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=artinfo&aid=$2;
}

# 链接详细页面
location ~ ^/linkinfo/(\d+)\.html$ {
    try_files $uri /index.php?mod=linkinfo&lid=$1;
}

# IP信息页面
location ~ ^/ipinfo/(.+)\.html$ {
    try_files $uri /index.php?mod=ipinfo&ip=$1;
}

# 单页
location ~ ^/diypage/(\d+)\.html$ {
    try_files $uri /index.php?mod=diypage&pid=$1;
}

# RSS订阅
location ~ ^/rssfeed/(.+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=rssfeed&type=$1&cid=$2;
}
location ~ ^/rssfeed/(.+)\.html$ {
    try_files $uri /index.php?mod=rssfeed&type=$1;
}

# 网站地图
location ~ ^/sitemap/(.+)-(\d+)\.html$ {
    try_files $uri /index.php?mod=sitemap&type=$1&cid=$2;
}
location ~ ^/sitemap\.xml$ {
    try_files $uri /index.php?mod=sitemap;
}
location ~ ^/sitemap/(.+)\.html$ {
    try_files $uri /index.php?mod=sitemap&type=$1;
}

# ==========================================
# 会员相关页面
# ==========================================

# 会员相关页面
location ~ ^/member/?$ {
    try_files $uri /member/index.php;
}
location ~ ^/member/(.+)$ {
    try_files $uri /member/index.php?mod=$1;
}

# ==========================================
# 特殊页面处理
# ==========================================

# 待审核页面
location ~ ^/pending/?$ {
    try_files $uri /index.php?mod=pending;
}
location ~ ^/pending/(\d+)/?$ {
    try_files $uri /index.php?mod=pending&page=$1;
}
location ~ ^/pending/detail/(\d+)/?$ {
    try_files $uri /index.php?mod=pending_detail&id=$1;
}

# 黑名单页面
location ~ ^/blacklist/?$ {
    try_files $uri /index.php?mod=blacklist;
}
location ~ ^/blacklist/(\d+)/?$ {
    try_files $uri /index.php?mod=blacklist&page=$1;
}
location ~ ^/blacklist/detail/(\d+)/?$ {
    try_files $uri /index.php?mod=blacklist_detail&id=$1;
}

# 被拒绝页面
location ~ ^/rejected/?$ {
    try_files $uri /index.php?mod=rejected;
}
location ~ ^/rejected/(\d+)/?$ {
    try_files $uri /index.php?mod=rejected&page=$1;
}
location ~ ^/rejected/detail/(\d+)/?$ {
    try_files $uri /index.php?mod=rejected_detail&id=$1;
}

# 快速提交
location ~ ^/addurl/?$ {
    try_files $uri /index.php?mod=addurl;
}
location ~ ^/quicksubmit/?$ {
    try_files $uri /index.php?mod=quicksubmit;
}

# 数据统计
location ~ ^/datastats/?$ {
    try_files $uri /index.php?mod=datastats;
}

# AJAX和API接口
location ~ ^/ajaxget/?$ {
    try_files $uri /index.php?mod=ajaxget;
}
location ~ ^/getdata/?$ {
    try_files $uri /index.php?mod=getdata;
}
location ~ ^/api/?$ {
    try_files $uri /index.php?mod=api;
}

# ==========================================
# 默认处理
# ==========================================

# 如果文件不存在，则交给index.php处理
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

# PHP文件处理
location ~ \.php$ {
    fastcgi_pass unix:/tmp/php-cgi.sock;
    fastcgi_index index.php;
    include fastcgi_params;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
}
