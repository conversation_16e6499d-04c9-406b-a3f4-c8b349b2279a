<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = 'VIP网站列表';
$pageurl = '?mod=vip_list';
$tempfile = 'vip_list.html';

$pagesize = 20;
$curpage = intval(isset($_GET['page']) ? $_GET['page'] : 1);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}

$cate_id = intval(isset($_GET['cid']) ? $_GET['cid'] : 0);
$cache_id = $cate_id.'-'.$curpage;

if ($cate_id > 0) {
	$pageurl .= '&cid='.$cate_id;
}

if (!$smarty->isCached($tempfile, $cache_id)) {
	// 构建查询条件
	$where = "w.web_status=3 AND w.web_ispay=1";
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!$cate) {
			unset($cate);
			redirect('?mod=category');
		}
		$where .= " AND w.cate_id=$cate_id";
		$smarty->assign('cate', $cate);
	}

	// 获取VIP网站列表
	$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_tags, w.web_ctime, w.cate_id, w.web_pic,
	               w.web_ispay, w.web_istop, w.web_isbest,
	               d.web_views, d.web_instat, d.web_outstat, d.web_utime,
	               c.cate_name
	        FROM " . $DB->table('websites') . " w
	        LEFT JOIN " . $DB->table('webdata') . " d ON w.web_id = d.web_id
	        LEFT JOIN " . $DB->table('categories') . " c ON w.cate_id = c.cate_id
	        WHERE $where
	        ORDER BY w.web_istop DESC, w.web_ctime DESC
	        LIMIT $start, $pagesize";

	$websites = $DB->fetch_all($sql);
	
	// 格式化数据
	foreach ($websites as &$web) {
		$web['web_furl'] = format_url($web['web_url']);
		$web['web_pic'] = get_webthumb($web['web_pic']);
		$web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
		$web['web_utime'] = date('Y-m-d', $web['web_utime']);
		// 使用伪静态URL
		$web['web_link'] = $options['site_root'] . 'vipdetail/' . $web['web_id'] . '.html';
		$web['cate_link'] = '?mod=vip_list&cid=' . $web['cate_id'];
		$web['vip_level'] = $web['web_istop'] ? 'SVIP' : 'VIP';
		$web['vip_badge_color'] = $web['web_istop'] ? '#ff6b35' : '#ffd700';
	}

	// 获取总数
	$total = $DB->get_count($DB->table('websites') . ' w', $where);

	// 分页
	$showpage = '';
	if ($total > $pagesize) {
		$pages = ceil($total / $pagesize);
		$base_url = $pageurl;
		
		$showpage = '<div class="pagination">';
		
		if ($curpage > 1) {
			$prev_page = $curpage - 1;
			$showpage .= '<a href="' . $base_url . '&page=' . $prev_page . '">上一页</a>';
		}
		
		$start_page = max(1, $curpage - 5);
		$end_page = min($pages, $curpage + 5);
		
		for ($i = $start_page; $i <= $end_page; $i++) {
			if ($i == $curpage) {
				$showpage .= '<span class="current">' . $i . '</span>';
			} else {
				$showpage .= '<a href="' . $base_url . '&page=' . $i . '">' . $i . '</a>';
			}
		}
		
		if ($curpage < $pages) {
			$next_page = $curpage + 1;
			$showpage .= '<a href="' . $base_url . '&page=' . $next_page . '">下一页</a>';
		}
		
		$showpage .= '</div>';
	}

	// 获取分类列表
	$categories = get_categories();

	// 优化SEO设置
	if ($cate_id > 0 && isset($cate)) {
		// 分类VIP页面SEO
		$seo_title = $cate['cate_name'] . 'VIP网站大全 - ' . $cate['cate_name'] . '优质网站推荐 - ' . $options['site_name'];

		$seo_keywords = array();
		if (!empty($cate['cate_keywords'])) {
			$cate_keywords = explode(',', $cate['cate_keywords']);
			$seo_keywords = array_merge($seo_keywords, $cate_keywords);
		}
		$seo_keywords[] = $cate['cate_name'] . 'VIP网站';
		$seo_keywords[] = $cate['cate_name'] . '优质网站';
		$seo_keywords[] = $cate['cate_name'] . '推荐网站';
		$seo_keywords[] = 'VIP认证';
		$seo_keywords[] = '付费网站';
		if (!empty($options['site_keywords'])) {
			$site_keywords = explode(',', $options['site_keywords']);
			$seo_keywords = array_merge($seo_keywords, $site_keywords);
		}

		$seo_description = $cate['cate_name'] . 'VIP网站大全，精选' . $cate['cate_name'] . '领域优质网站';
		if ($total > 0) {
			$seo_description .= '，共收录' . $total . '个VIP认证网站';
		}
		$seo_description .= '。享受优质服务和专属特权，' . $options['site_name'] . '为您推荐最优质的' . $cate['cate_name'] . '网站。';
	} else {
		// 全部VIP页面SEO
		$seo_title = 'VIP网站大全 - 优质网站推荐 - ' . $options['site_name'];

		$seo_keywords = array('VIP网站', '优质网站', '推荐网站', 'VIP认证', '付费网站', '精品网站', '网站推荐');
		if (!empty($options['site_keywords'])) {
			$site_keywords = explode(',', $options['site_keywords']);
			$seo_keywords = array_merge($seo_keywords, $site_keywords);
		}

		$seo_description = 'VIP网站大全，汇聚各行业优质网站';
		if ($total > 0) {
			$seo_description .= '，共收录' . $total . '个VIP认证网站';
		}
		$seo_description .= '。享受优质服务和专属特权，' . $options['site_name'] . '为您推荐最值得信赖的优质网站。';
	}

	// 确保描述长度适中
	if (mb_strlen($seo_description, 'UTF-8') > 160) {
		$seo_description = mb_substr($seo_description, 0, 157, 'UTF-8') . '...';
	}

	// 设置模板变量
	$smarty->assign('site_title', $seo_title);
	$smarty->assign('site_keywords', implode(',', array_unique($seo_keywords)));
	$smarty->assign('site_description', $seo_description);
	$smarty->assign('site_path', get_sitepath() . ' &raquo; ' . $pagename);
	$smarty->assign('site_rss', get_rssfeed());

	$smarty->assign('websites', $websites);
	$smarty->assign('categories', $categories);
	$smarty->assign('current_category', $cate_id);
	$smarty->assign('showpage', $showpage);
	$smarty->assign('total', $total);
	$smarty->assign('pagename', $pagename);
}

smarty_output($tempfile, $cache_id);
?>
