<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '搜索结果';
$pageurl = '?mod=search';
$tempfile = 'search.html';
$table = $DB->table('websites');

//搜索页不缓存
$smarty->caching = false;

$pagesize = 10;
$curpage = intval($_GET['page']);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
		
$strtype = htmlspecialchars(strtolower(trim($_GET['type'])));
$keyword = htmlspecialchars(addslashes(trim($_GET['query'])));

if (!$smarty->isCached($tempfile)) {
	if ($keyword) {
		$pageurl .= '&type='.$strtype.'&query='.urlencode($keyword);

		$smarty->assign('site_title', $keyword.' - '.$pagename.' - '.$options['site_name']);
		$smarty->assign('site_keywords', $keyword.'，搜索结果，查询结果');
		$smarty->assign('site_description', '以下是与关键字(词)“'.$keyword.'”相关的结果。');
		$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename.' &raquo; <font color="#ff0000">'.$keyword.'</font>');
		$smarty->assign('rss_feed', get_rssfeed());

		// 判断搜索类型：文章还是网站
		if ($strtype == 'article') {
			// 文章搜索
			$table = $DB->table('articles');
			$where = "a.art_status=3 AND (a.art_title like '%$keyword%' OR a.art_tags like '%$keyword%' OR a.art_intro like '%$keyword%')";

			$articles = get_article_list($where, 'ctime', 'DESC', $start, $pagesize);
			$total = $DB->get_count($table.' a LEFT JOIN '.$DB->table('categories').' c ON a.cate_id=c.cate_id', $where);
			$showpage = showpage($pageurl, $total, $curpage, $pagesize);

			$smarty->assign('search_type', 'article');
			$smarty->assign('articles', $articles);
			$smarty->assign('websites', array()); // 空数组
			unset($articles);
		} else {
			// 网站搜索
			$table = $DB->table('websites');
			$where = "w.web_status=3";

			switch ($strtype) {
				case 'name' :
					$where .= " AND w.web_name like '%$keyword%'";
					break;
				case 'url' :
					$where .= " AND w.web_url like '%$keyword%'";
					break;
				case 'tags' :
					$where .= " AND w.web_tags like '%$keyword%'";
					break;
				case 'intro' :
					$where .= " AND w.web_intro like '%$keyword%'";
					break;
				default :
					$where .= " AND w.web_name like '%$keyword%'";
					break;
			}

			$websites = get_website_list($where, 'web_ctime', 'DESC', $start, $pagesize);
			$total = $DB->get_count($table.' w', $where);
			$showpage = showpage($pageurl, $total, $curpage, $pagesize);

			$smarty->assign('search_type', 'website');
			$smarty->assign('websites', $websites);
			$smarty->assign('articles', array()); // 空数组
			unset($websites);
		}

		$smarty->assign('pagename', $pagename);
		$smarty->assign('category_list', get_categories());
		$smarty->assign('archives', get_archives());
		$smarty->assign('keyword', $keyword);
		$smarty->assign('total', $total);
		$smarty->assign('showpage', $showpage);
	} else {
		// 没有关键词时显示空结果
		$smarty->assign('search_type', 'website');
		$smarty->assign('websites', array());
		$smarty->assign('articles', array());
		$smarty->assign('total', 0);
		$smarty->assign('showpage', '');
	}
}
	
smarty_output($tempfile, $cache_id);
?>