<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '审核不通过';
$pageurl = '?mod=rejected';
$tempfile = 'rejected.html';

$pagesize = 20;
$curpage = intval(isset($_GET['page']) ? $_GET['page'] : 1);
if ($curpage > 1) {
    $start = ($curpage - 1) * $pagesize;
} else {
    $start = 0;
    $curpage = 1;
}

$cate_id = intval(isset($_GET['cid']) ? $_GET['cid'] : 0);
$cache_id = $cate_id . '-' . $curpage;

if ($cate_id > 0) {
    $pageurl .= '&cid=' . $cate_id;
}

if (!$smarty->isCached($tempfile, $cache_id)) {
    // 构建查询条件 - 只查询审核不通过状态的网站 (web_status=4)
    $where = "w.web_status=4";

    // 分类筛选
    if ($cate_id > 0) {
        $cate = $DB->fetch_one("SELECT cate_id, root_id, cate_name, cate_arrchildid, cate_childcount FROM ".$DB->table('categories')." WHERE cate_id=$cate_id LIMIT 1");
        if (!$cate) {
            // 如果分类不存在，重定向到全部分类
            header('Location: ?mod=rejected');
            exit;
        }

        // 添加分类条件
        if ($cate['cate_childcount'] > 0) {
            $where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
        } else {
            $where .= " AND w.cate_id=$cate_id";
        }

        $current_cate_name = $cate['cate_name'];
    } else {
        $current_cate_name = '全部分类';
    }

    // 检查web_reject_reason字段是否存在
    $table_name = $DB->table('websites');
    $check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_reject_reason'";
    $check_result = $DB->query($check_sql);
    $has_reject_reason_field = $DB->num_rows($check_result) > 0;
	
    // 获取审核不通过网站列表
    if ($has_reject_reason_field) {
        $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_pic, w.web_tags, w.web_ctime, w.cate_id, w.web_reject_reason, c.cate_name
                FROM ".$DB->table('websites')." w
                LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id
                WHERE $where
                ORDER BY w.web_ctime DESC
                LIMIT $start, $pagesize";
    } else {
        $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_pic, w.web_tags, w.web_ctime, w.cate_id, c.cate_name
                FROM ".$DB->table('websites')." w
                LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id
                WHERE $where
                ORDER BY w.web_ctime DESC
                LIMIT $start, $pagesize";
    }

    $websites = $DB->fetch_all($sql);

    // 格式化数据
    foreach ($websites as &$web) {
        // 确保审核不通过字段有默认值
        if (!$has_reject_reason_field || !isset($web['web_reject_reason'])) {
            $web['web_reject_reason'] = '';
        }
        if (empty($web['web_reject_reason'])) {
            $web['web_reject_reason'] = '该网站未通过审核';
        }

        $web['web_pic'] = get_webthumb($web['web_pic']);

        // 保存原始时间戳用于判断是否为今天
        $original_ctime = $web['web_ctime'];

        // 格式化显示时间
        $web['web_ctime'] = date('Y-m-d', intval($original_ctime));
        // 使用伪静态URL
        $web['web_link'] = $options['site_root'] . 'rejecteddetail/' . $web['web_id'] . '.html';
        $web['cate_link'] = '?mod=rejected&cid=' . $web['cate_id'];

        // 处理简介，限制长度
        if (!empty($web['web_intro'])) {
            $web['web_intro'] = mb_substr(strip_tags($web['web_intro']), 0, 100, 'utf-8');
            if (mb_strlen($web['web_intro'], 'utf-8') >= 100) {
                $web['web_intro'] .= '...';
            }
        }

        // 隐藏网站URL，只显示域名
        $url_to_parse = $web['web_url'];
        if (!preg_match('/^https?:\/\//', $url_to_parse)) {
            $url_to_parse = 'http://' . $url_to_parse;
        }
        $web['domain'] = parse_url($url_to_parse, PHP_URL_HOST);
        if (!$web['domain']) {
            $web['domain'] = $web['web_url'];
        }

        // 判断是否为今天提交的网站
        $web['is_today'] = (date('Y-m-d', intval($original_ctime)) == date('Y-m-d'));
    }

    // 获取总数
    $total = $DB->get_count($DB->table('websites') . ' w', $where);

    // 分页处理
    $showpage = '';
    if ($total > $pagesize) {
        $pages = ceil($total / $pagesize);
        $base_url = $pageurl;

        $showpage = '<div class="pagination">';

        if ($curpage > 1) {
            $prev_page = $curpage - 1;
            $showpage .= '<a href="' . $base_url . '&page=' . $prev_page . '">上一页</a>';
        }

        $start_page = max(1, $curpage - 5);
        $end_page = min($pages, $curpage + 5);

        for ($i = $start_page; $i <= $end_page; $i++) {
            if ($i == $curpage) {
                $showpage .= '<span class="current">' . $i . '</span>';
            } else {
                $showpage .= '<a href="' . $base_url . '&page=' . $i . '">' . $i . '</a>';
            }
        }

        if ($curpage < $pages) {
            $next_page = $curpage + 1;
            $showpage .= '<a href="' . $base_url . '&page=' . $next_page . '">下一页</a>';
        }

        $showpage .= '</div>';
    }

    // 获取分类列表
    $categories = $DB->fetch_all("SELECT cate_id, cate_name FROM ".$DB->table('categories')." WHERE root_id=0 AND cate_mod='webdir' ORDER BY cate_order ASC, cate_id ASC");

    // SEO设置
    $seo_title = '审核不通过网站 - ' . $current_cate_name . ' - ' . $options['site_name'];
    $seo_keywords = '审核不通过,网站审核失败,审核未通过,网站收录失败';
    $seo_description = '展示审核不通过的网站列表，共' . $total . '个网站因各种原因未能通过审核标准。';

    // 设置模板变量
    $smarty->assign('site_title', $seo_title);
    $smarty->assign('site_keywords', $seo_keywords);
    $smarty->assign('site_description', $seo_description);
    $smarty->assign('site_path', get_sitepath() . ' &raquo; ' . $pagename);
    $smarty->assign('site_rss', get_rssfeed());

    $smarty->assign('websites', $websites);
    $smarty->assign('categories', $categories);
    $smarty->assign('current_cate_id', $cate_id);
    $smarty->assign('current_cate_name', $current_cate_name);
    $smarty->assign('showpage', $showpage);
    $smarty->assign('total', $total);
    $smarty->assign('pagename', $pagename);
}

smarty_output($tempfile, $cache_id);
?>
