# ===== 修正后的伪静态规则 =====
# 专门修正VIP、待审核、黑名单、审核不通过页面的链接问题

# 基础模块（保持原有）
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|blacklist|pending|rejected|addurl|quicksubmit|datastats)(/?)$ /index.php?mod=$1;

# 最近更新模块
rewrite ^/update/(\d+)\.html$ /index.php?mod=update&days=$1;
rewrite ^/update/(\d+)-(\d+)\.html$ /index.php?mod=update&days=$1&page=$2;

# 数据归档模块
rewrite ^/archives/(\d+)\.html$ /index.php?mod=archives&date=$1;
rewrite ^/archives/(\d+)-(\d+)\.html$ /index.php?mod=archives&date=$1&page=$2;

# 站内搜索模块
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 快速搜索
rewrite ^/(br|pr)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/(br|pr)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 网站详情页
rewrite ^/view/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo-(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/site/(\d+)-(.+)(/?)\.html$ /index.php?mod=siteinfo&wid=$1;

# 文章详情页
rewrite ^/artinfo/(\d+)\.html$ /index.php?mod=artinfo&aid=$1;

# 友情链接详情页
rewrite ^/linkinfo/(\d+)\.html$ /index.php?mod=linkinfo&lid=$1;

# 自定义单页
rewrite ^/diypage/(\d+)\.html$ /index.php?mod=diypage&pid=$1;

# ===== 修正的VIP网站相关规则 =====
# VIP列表页
rewrite ^/vip/?$ /index.php?mod=vip_list;
rewrite ^/vip/list/?$ /index.php?mod=vip_list;
rewrite ^/vip/list/(\d+)/?$ /index.php?mod=vip_list&page=$1;
rewrite ^/vip/(\d+)\.html$ /index.php?mod=vip_list&page=$1;

# VIP分类页
rewrite ^/vip/category/(\d+)/?$ /index.php?mod=vip_list&cid=$1;
rewrite ^/vip/category/(\d+)/(\d+)/?$ /index.php?mod=vip_list&cid=$1&page=$2;
rewrite ^/vip/category/(\d+)-(\d+)\.html$ /index.php?mod=vip_list&cid=$1&page=$2;

# VIP详情页（支持多种格式）
rewrite ^/vip/detail/(\d+)/?$ /index.php?mod=vip_detail&id=$1;
rewrite ^/vip/detail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1;
rewrite ^/vipdetail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1;

# ===== 修正的待审核网站相关规则 =====
# 待审核列表页
rewrite ^/pending/?$ /index.php?mod=pending;
rewrite ^/pending/(\d+)/?$ /index.php?mod=pending&page=$1;
rewrite ^/pending/(\d+)\.html$ /index.php?mod=pending&page=$1;

# 待审核分类页
rewrite ^/pending/category/(\d+)/?$ /index.php?mod=pending&cid=$1;
rewrite ^/pending/category/(\d+)/(\d+)/?$ /index.php?mod=pending&cid=$1&page=$2;
rewrite ^/pending/category/(\d+)-(\d+)\.html$ /index.php?mod=pending&cid=$1&page=$2;

# 待审核详情页（支持多种格式）
rewrite ^/pending/detail/(\d+)/?$ /index.php?mod=pending_detail&id=$1;
rewrite ^/pending/detail/(\d+)\.html$ /index.php?mod=pending_detail&id=$1;
rewrite ^/pendingdetail/(\d+)\.html$ /index.php?mod=pending_detail&id=$1;

# ===== 修正的黑名单网站相关规则 =====
# 黑名单列表页
rewrite ^/blacklist/?$ /index.php?mod=blacklist;
rewrite ^/blacklist/(\d+)/?$ /index.php?mod=blacklist&page=$1;
rewrite ^/blacklist/(\d+)\.html$ /index.php?mod=blacklist&page=$1;

# 黑名单分类页
rewrite ^/blacklist/category/(\d+)/?$ /index.php?mod=blacklist&category=$1;
rewrite ^/blacklist/category/(\d+)/(\d+)/?$ /index.php?mod=blacklist&category=$1&page=$2;
rewrite ^/blacklist/category/(\d+)-(\d+)\.html$ /index.php?mod=blacklist&category=$1&page=$2;

# 黑名单详情页（支持多种格式）
rewrite ^/blacklist/detail/(\d+)/?$ /index.php?mod=blacklist_detail&id=$1;
rewrite ^/blacklist/detail/(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1;
rewrite ^/blacklistdetail/(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1;

# ===== 修正的审核不通过网站相关规则 =====
# 审核不通过列表页
rewrite ^/rejected/?$ /index.php?mod=rejected;
rewrite ^/rejected/(\d+)/?$ /index.php?mod=rejected&page=$1;
rewrite ^/rejected/(\d+)\.html$ /index.php?mod=rejected&page=$1;

# 审核不通过分类页
rewrite ^/rejected/category/(\d+)/?$ /index.php?mod=rejected&cid=$1;
rewrite ^/rejected/category/(\d+)/(\d+)/?$ /index.php?mod=rejected&cid=$1&page=$2;
rewrite ^/rejected/category/(\d+)-(\d+)\.html$ /index.php?mod=rejected&cid=$1&page=$2;

# 审核不通过详情页（支持多种格式）
rewrite ^/rejected/detail/(\d+)/?$ /index.php?mod=rejected_detail&id=$1;
rewrite ^/rejected/detail/(\d+)\.html$ /index.php?mod=rejected_detail&id=$1;
rewrite ^/rejecteddetail/(\d+)\.html$ /index.php?mod=rejected_detail&id=$1;

# RSS订阅
rewrite ^/rssfeed/(\d+)\.html$ /index.php?mod=rssfeed&cid=$1;
rewrite ^/rssfeed/(.+)/(\d+)\.html$ /index.php?mod=rssfeed&cid=$2;
rewrite ^/rssfeed/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rssfeed&cid=$2&page=$3;

# 网站地图
rewrite ^/sitemap/(\d+)\.html$ /index.php?mod=sitemap&cid=$1;

# 网站目录分类页面（支持排序）
rewrite ^/webdir/(.+)/(\d+)\.html$ /index.php?mod=webdir&cid=$2;
rewrite ^/webdir/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3;
rewrite ^/webdir/(.+)/(\d+)-(instat|outstat|views|ctime)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&sort=$3&page=$4;

# 友情链接分类页面（支持排序）
rewrite ^/weblink/(.+)/(\d+)\.html$ /index.php?mod=weblink&cid=$2;
rewrite ^/weblink/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3;
rewrite ^/weblink/(.+)/(\d+)-(time|id)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&sort=$3&page=$4;

# 文章分类页面
rewrite ^/article/(.+)/(\d+)\.html$ /index.php?mod=article&cid=$2;
rewrite ^/article/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3;

# 网站提交相关
rewrite ^/submit/?$ /index.php?mod=addurl;
rewrite ^/quicksubmit/?$ /index.php?mod=quicksubmit;

# 数据统计页面
rewrite ^/stats/?$ /index.php?mod=datastats;

# 网站图标获取
rewrite ^/ico/(.*)\.png$ /ico/get.php?url=$1;

# 网站快照
rewrite ^/snapshot(-|\/)(.*)$ /snapshot.php?site=$2;

# API接口
rewrite ^/api/(.+)$ /index.php?mod=api&action=$1;

# AJAX数据获取
rewrite ^/ajaxget/(.+)$ /index.php?mod=ajaxget&type=$1;
rewrite ^/getdata/(.+)$ /index.php?mod=getdata&type=$1;

# ===== 问题修正说明 =====
# 1. VIP详情页：支持 /vip/detail/123.html 和 /vipdetail/123.html 两种格式
# 2. 待审核详情页：支持 /pending/detail/123.html 和 /pendingdetail/123.html 两种格式
# 3. 黑名单详情页：支持 /blacklist/detail/123.html 和 /blacklistdetail/123.html 两种格式
# 4. 审核不通过详情页：支持 /rejected/detail/123.html 和 /rejecteddetail/123.html 两种格式
# 5. 所有页面都支持带斜杠和不带斜杠的URL格式
# 6. 分页支持多种格式：/page/2/ 和 /page-2.html
# 7. 分类页面支持 /category/1/ 和 /category/1-2.html 格式

# ===== 测试URL示例 =====
# VIP相关：
# /vip/ -> ?mod=vip_list
# /vip/2.html -> ?mod=vip_list&page=2
# /vip/category/1/ -> ?mod=vip_list&cid=1
# /vip/detail/123.html -> ?mod=vip_detail&id=123

# 待审核相关：
# /pending/ -> ?mod=pending
# /pending/2.html -> ?mod=pending&page=2
# /pending/detail/123.html -> ?mod=pending_detail&id=123

# 黑名单相关：
# /blacklist/ -> ?mod=blacklist
# /blacklist/2.html -> ?mod=blacklist&page=2
# /blacklist/detail/123.html -> ?mod=blacklist_detail&id=123

# 审核不通过相关：
# /rejected/ -> ?mod=rejected
# /rejected/2.html -> ?mod=rejected&page=2
# /rejected/detail/123.html -> ?mod=rejected_detail&id=123
