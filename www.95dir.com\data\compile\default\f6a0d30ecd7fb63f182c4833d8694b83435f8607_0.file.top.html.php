<?php
/* Smarty version 4.5.5, created on 2025-07-30 16:05:44
  from '/www/wwwroot/www.95dir.com/themes/default/top.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6889d258a5d5a4_79525828',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'f6a0d30ecd7fb63f182c4833d8694b83435f8607' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/top.html',
      1 => 1753851170,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:script.html' => 1,
    'file:topbar.html' => 1,
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6889d258a5d5a4_79525828 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
<head>
<title><?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<?php $_smarty_tpl->_subTemplateRender("file:script.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</head>

<body>
<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<div id="wrapper">
	<?php $_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    <div id="mainbox" class="clearfix">
    	    <div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>VIP特权排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,true,false,'views','desc'), 'quick', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['quick']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_furl'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
            <div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>推荐排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,false,true,'views','desc'), 'quick', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['quick']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
    	    <div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>电脑网络排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(3,10,false,false,'views','desc'), 'hot', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
            <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>生活服务排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(4,10,false,false,'views','desc'), 'hot', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
            <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>休闲娱乐排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(5,10,false,false,'views','desc'), 'hot', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
    	    <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>教育文化排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(6,10,false,false,'views','desc'), 'hot', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
            <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>行业企业排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(7,10,false,false,'views','desc'), 'hot', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
            <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>综合其他排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(8,10,false,false,'views','desc'), 'hot', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
    	    <div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>人工智能排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(1,10,false,false,'views','desc'), 'hot', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        	<div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>入站排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,false,false,'instat','desc'), 'instat', false, NULL, 'instat_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['instat']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['instat']->value) {
$_smarty_tpl->tpl_vars['instat']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_instat_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_instat_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_instat_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['instat']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['instat']->value['web_instat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        	<div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>出站排行榜 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,false,false,'outstat','desc'), 'outstat', false, NULL, 'outstat_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['outstat']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['outstat']->value) {
$_smarty_tpl->tpl_vars['outstat']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_outstat_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_outstat_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_outstat_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['outstat']->value['web_outstat'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        	<div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>最新收录 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,false,false,'ctime'), 'new', false, NULL, 'new_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['new']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_new_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_new_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_new_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['new']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['new']->value['web_views'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        	<div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>热门浏览 TOP10</h3>
                <ul class="toplist">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,false,false,'views','desc'), 'hot', false, NULL, 'hot_website', array (
  'iteration' => true,
));
$_smarty_tpl->tpl_vars['hot']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['hot']->value) {
$_smarty_tpl->tpl_vars['hot']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']++;
?>
                   	<li><span><?php echo (isset($_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_hot_website']->value['iteration'] : null);?>
.</span><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_name'];?>
</a> - <em><a href="<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_link'];?>
" target="_blank" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['hot']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_url'];?>
</a></em><em1><?php echo $_smarty_tpl->tpl_vars['hot']->value['web_views'];?>
</em1></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        <!--<div id="mainbox-right">
            <div style="height: 250px;">
            <?php echo get_adcode(7);?>

            </div>
            <div style="margin-top: 15px;"><?php echo get_adcode(7);?>
</div>
        </div>-->
    </div>
    <?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</div>
</body>
</html><?php }
}
