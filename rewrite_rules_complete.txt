# ===== 95分类目录完整伪静态规则 =====
# 适用于Nginx服务器，支持所有模块的SEO友好URL

# 基础模块（首页、分类浏览、数据归档、最近更新、排行榜、意见反馈等）
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|blacklist|pending|rejected|addurl|quicksubmit|datastats)(/?)$ /index.php?mod=$1;

# 最近更新模块
rewrite ^/update/(\d+)\.html$ /index.php?mod=update&days=$1;
rewrite ^/update/(\d+)-(\d+)\.html$ /index.php?mod=update&days=$1&page=$2;

# 数据归档模块
rewrite ^/archives/(\d+)\.html$ /index.php?mod=archives&date=$1;
rewrite ^/archives/(\d+)-(\d+)\.html$ /index.php?mod=archives&date=$1&page=$2;

# 站内搜索模块（支持多种搜索类型）
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 快速搜索（BR/PR值搜索）
rewrite ^/(br|pr)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/(br|pr)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 网站详情页（多种URL格式支持）
rewrite ^/view/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo-(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/site/(\d+)-(.+)(/?)\.html$ /index.php?mod=siteinfo&wid=$1;

# 文章详情页
rewrite ^/artinfo/(\d+)\.html$ /index.php?mod=artinfo&aid=$1;

# 友情链接详情页
rewrite ^/linkinfo/(\d+)\.html$ /index.php?mod=linkinfo&lid=$1;

# 自定义单页
rewrite ^/diypage/(\d+)\.html$ /index.php?mod=diypage&pid=$1;

# VIP网站相关（新增）
rewrite ^/vip/?$ /index.php?mod=vip_list;
rewrite ^/vip/list/?$ /index.php?mod=vip_list;
rewrite ^/vip/list/(\d+)/?$ /index.php?mod=vip_list&page=$1;
rewrite ^/vip/(\d+)/?$ /index.php?mod=vip_list&page=$1;
rewrite ^/vip/category/(\d+)/?$ /index.php?mod=vip_list&cid=$1;
rewrite ^/vip/category/(\d+)/(\d+)/?$ /index.php?mod=vip_list&cid=$1&page=$2;
rewrite ^/vip/detail/(\d+)/?$ /index.php?mod=vip_detail&id=$1;
rewrite ^/vip/detail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1;

# 待审核网站相关（新增）
rewrite ^/pending/?$ /index.php?mod=pending;
rewrite ^/pending/(\d+)/?$ /index.php?mod=pending&page=$1;
rewrite ^/pending/(\d+)\.html$ /index.php?mod=pending&page=$1;
rewrite ^/pending/category/(\d+)/?$ /index.php?mod=pending&cid=$1;
rewrite ^/pending/category/(\d+)/(\d+)/?$ /index.php?mod=pending&cid=$1&page=$2;
rewrite ^/pending/category/(\d+)-(\d+)\.html$ /index.php?mod=pending&cid=$1&page=$2;
rewrite ^/pending/detail/(\d+)/?$ /index.php?mod=pending_detail&id=$1;
rewrite ^/pending/detail/(\d+)\.html$ /index.php?mod=pending_detail&id=$1;

# 黑名单网站相关（新增）
rewrite ^/blacklist/?$ /index.php?mod=blacklist;
rewrite ^/blacklist/(\d+)/?$ /index.php?mod=blacklist&page=$1;
rewrite ^/blacklist/(\d+)\.html$ /index.php?mod=blacklist&page=$1;
rewrite ^/blacklist/category/(\d+)/?$ /index.php?mod=blacklist&category=$1;
rewrite ^/blacklist/category/(\d+)/(\d+)/?$ /index.php?mod=blacklist&category=$1&page=$2;
rewrite ^/blacklist/category/(\d+)-(\d+)\.html$ /index.php?mod=blacklist&category=$1&page=$2;
rewrite ^/blacklist/detail/(\d+)/?$ /index.php?mod=blacklist_detail&id=$1;
rewrite ^/blacklist/detail/(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1;

# 审核不通过网站相关（新增）
rewrite ^/rejected/?$ /index.php?mod=rejected;
rewrite ^/rejected/(\d+)/?$ /index.php?mod=rejected&page=$1;
rewrite ^/rejected/(\d+)\.html$ /index.php?mod=rejected&page=$1;
rewrite ^/rejected/category/(\d+)/?$ /index.php?mod=rejected&cid=$1;
rewrite ^/rejected/category/(\d+)/(\d+)/?$ /index.php?mod=rejected&cid=$1&page=$2;
rewrite ^/rejected/category/(\d+)-(\d+)\.html$ /index.php?mod=rejected&cid=$1&page=$2;
rewrite ^/rejected/detail/(\d+)/?$ /index.php?mod=rejected_detail&id=$1;
rewrite ^/rejected/detail/(\d+)\.html$ /index.php?mod=rejected_detail&id=$1;

# RSS订阅
rewrite ^/rssfeed/(\d+)\.html$ /index.php?mod=rssfeed&cid=$1;
rewrite ^/rssfeed/(.+)/(\d+)\.html$ /index.php?mod=rssfeed&cid=$2;
rewrite ^/rssfeed/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rssfeed&cid=$2&page=$3;

# 网站地图
rewrite ^/sitemap/(\d+)\.html$ /index.php?mod=sitemap&cid=$1;

# 网站目录分类页面（支持排序）
rewrite ^/webdir/(.+)/(\d+)\.html$ /index.php?mod=webdir&cid=$2;
rewrite ^/webdir/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3;
rewrite ^/webdir/(.+)/(\d+)-(instat|outstat|views|ctime)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&sort=$3&page=$4;

# 友情链接分类页面（支持排序）
rewrite ^/weblink/(.+)/(\d+)\.html$ /index.php?mod=weblink&cid=$2;
rewrite ^/weblink/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3;
rewrite ^/weblink/(.+)/(\d+)-(time|id)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&sort=$3&page=$4;

# 文章分类页面
rewrite ^/article/(.+)/(\d+)\.html$ /index.php?mod=article&cid=$2;
rewrite ^/article/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3;

# VIP网站相关（按现有风格）
rewrite ^/vip/(.+)/(\d+)\.html$ /index.php?mod=vip_list&cid=$2;
rewrite ^/vip/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=vip_list&cid=$2&page=$3;
rewrite ^/vipdetail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1;

# 待审核网站相关（按现有风格）
rewrite ^/pending/(.+)/(\d+)\.html$ /index.php?mod=pending&cid=$2;
rewrite ^/pending/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=pending&cid=$2&page=$3;
rewrite ^/pendingdetail/(\d+)\.html$ /index.php?mod=pending_detail&id=$1;

# 黑名单网站相关（按现有风格）
rewrite ^/blacklist/(.+)/(\d+)\.html$ /index.php?mod=blacklist&category=$2;
rewrite ^/blacklist/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=blacklist&category=$2&page=$3;
rewrite ^/blacklistdetail/(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1;

# 审核不通过网站相关（按现有风格）
rewrite ^/rejected/(.+)/(\d+)\.html$ /index.php?mod=rejected&cid=$2;
rewrite ^/rejected/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rejected&cid=$2&page=$3;
rewrite ^/rejecteddetail/(\d+)\.html$ /index.php?mod=rejected_detail&id=$1;

# 网站提交相关（新增）
rewrite ^/submit/?$ /index.php?mod=addurl;
rewrite ^/quicksubmit/?$ /index.php?mod=quicksubmit;

# 数据统计页面（新增）
rewrite ^/stats/?$ /index.php?mod=datastats;

# 网站图标获取
rewrite ^/ico/(.*)\.png$ /ico/get.php?url=$1;

# 网站快照
rewrite ^/snapshot(-|\/)(.*)$ /snapshot.php?site=$2;

# API接口
rewrite ^/api/(.+)$ /index.php?mod=api&action=$1;

# AJAX数据获取
rewrite ^/ajaxget/(.+)$ /index.php?mod=ajaxget&type=$1;
rewrite ^/getdata/(.+)$ /index.php?mod=getdata&type=$1;

# ===== Apache .htaccess 版本 =====
# 如果您使用Apache服务器，请使用以下规则替换上面的Nginx规则：

# RewriteEngine On
# RewriteBase /

# 基础模块
# RewriteRule ^(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|blacklist|pending|rejected|addurl|quicksubmit|datastats)/?$ index.php?mod=$1 [L,QSA]

# 最近更新模块
# RewriteRule ^update/([0-9]+)\.html$ index.php?mod=update&days=$1 [L,QSA]
# RewriteRule ^update/([0-9]+)-([0-9]+)\.html$ index.php?mod=update&days=$1&page=$2 [L,QSA]

# 数据归档模块
# RewriteRule ^archives/([0-9]+)\.html$ index.php?mod=archives&date=$1 [L,QSA]
# RewriteRule ^archives/([0-9]+)-([0-9]+)\.html$ index.php?mod=archives&date=$1&page=$2 [L,QSA]

# 站内搜索模块
# RewriteRule ^search/(name|url|tags|intro|br|pr|art)/(.+)-([0-9]+)\.html$ index.php?mod=search&type=$1&query=$2&page=$3 [L,QSA]
# RewriteRule ^search/(name|url|tags|intro|br|pr|art)/(.+)\.html$ index.php?mod=search&type=$1&query=$2 [L,QSA]

# 快速搜索
# RewriteRule ^(br|pr)/(.+)-([0-9]+)\.html$ index.php?mod=search&type=$1&query=$2&page=$3 [L,QSA]
# RewriteRule ^(br|pr)/(.+)\.html$ index.php?mod=search&type=$1&query=$2 [L,QSA]

# 网站详情页
# RewriteRule ^view/([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L,QSA]
# RewriteRule ^siteinfo/([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L,QSA]
# RewriteRule ^siteinfo-([0-9]+)\.html$ index.php?mod=siteinfo&wid=$1 [L,QSA]
# RewriteRule ^site/([0-9]+)-(.+)/?\.html$ index.php?mod=siteinfo&wid=$1 [L,QSA]

# 文章详情页
# RewriteRule ^artinfo/([0-9]+)\.html$ index.php?mod=artinfo&aid=$1 [L,QSA]

# 友情链接详情页
# RewriteRule ^linkinfo/([0-9]+)\.html$ index.php?mod=linkinfo&lid=$1 [L,QSA]

# 自定义单页
# RewriteRule ^diypage/([0-9]+)\.html$ index.php?mod=diypage&pid=$1 [L,QSA]

# VIP网站相关
# RewriteRule ^vip/?$ index.php?mod=vip_list [L,QSA]
# RewriteRule ^vip/list/?$ index.php?mod=vip_list [L,QSA]
# RewriteRule ^vip/list/([0-9]+)/?$ index.php?mod=vip_list&page=$1 [L,QSA]
# RewriteRule ^vip/([0-9]+)/?$ index.php?mod=vip_list&page=$1 [L,QSA]
# RewriteRule ^vip/category/([0-9]+)/?$ index.php?mod=vip_list&cid=$1 [L,QSA]
# RewriteRule ^vip/category/([0-9]+)/([0-9]+)/?$ index.php?mod=vip_list&cid=$1&page=$2 [L,QSA]
# RewriteRule ^vip/detail/([0-9]+)/?$ index.php?mod=vip_detail&id=$1 [L,QSA]
# RewriteRule ^vip/detail/([0-9]+)\.html$ index.php?mod=vip_detail&id=$1 [L,QSA]

# 待审核网站相关
# RewriteRule ^pending/?$ index.php?mod=pending [L,QSA]
# RewriteRule ^pending/([0-9]+)/?$ index.php?mod=pending&page=$1 [L,QSA]
# RewriteRule ^pending/([0-9]+)\.html$ index.php?mod=pending&page=$1 [L,QSA]
# RewriteRule ^pending/category/([0-9]+)/?$ index.php?mod=pending&cid=$1 [L,QSA]
# RewriteRule ^pending/category/([0-9]+)/([0-9]+)/?$ index.php?mod=pending&cid=$1&page=$2 [L,QSA]
# RewriteRule ^pending/category/([0-9]+)-([0-9]+)\.html$ index.php?mod=pending&cid=$1&page=$2 [L,QSA]
# RewriteRule ^pending/detail/([0-9]+)/?$ index.php?mod=pending_detail&id=$1 [L,QSA]
# RewriteRule ^pending/detail/([0-9]+)\.html$ index.php?mod=pending_detail&id=$1 [L,QSA]

# 黑名单网站相关
# RewriteRule ^blacklist/?$ index.php?mod=blacklist [L,QSA]
# RewriteRule ^blacklist/([0-9]+)/?$ index.php?mod=blacklist&page=$1 [L,QSA]
# RewriteRule ^blacklist/([0-9]+)\.html$ index.php?mod=blacklist&page=$1 [L,QSA]
# RewriteRule ^blacklist/category/([0-9]+)/?$ index.php?mod=blacklist&category=$1 [L,QSA]
# RewriteRule ^blacklist/category/([0-9]+)/([0-9]+)/?$ index.php?mod=blacklist&category=$1&page=$2 [L,QSA]
# RewriteRule ^blacklist/category/([0-9]+)-([0-9]+)\.html$ index.php?mod=blacklist&category=$1&page=$2 [L,QSA]
# RewriteRule ^blacklist/detail/([0-9]+)/?$ index.php?mod=blacklist_detail&id=$1 [L,QSA]
# RewriteRule ^blacklist/detail/([0-9]+)\.html$ index.php?mod=blacklist_detail&id=$1 [L,QSA]

# 审核不通过网站相关
# RewriteRule ^rejected/?$ index.php?mod=rejected [L,QSA]
# RewriteRule ^rejected/([0-9]+)/?$ index.php?mod=rejected&page=$1 [L,QSA]
# RewriteRule ^rejected/([0-9]+)\.html$ index.php?mod=rejected&page=$1 [L,QSA]
# RewriteRule ^rejected/category/([0-9]+)/?$ index.php?mod=rejected&cid=$1 [L,QSA]
# RewriteRule ^rejected/category/([0-9]+)/([0-9]+)/?$ index.php?mod=rejected&cid=$1&page=$2 [L,QSA]
# RewriteRule ^rejected/category/([0-9]+)-([0-9]+)\.html$ index.php?mod=rejected&cid=$1&page=$2 [L,QSA]
# RewriteRule ^rejected/detail/([0-9]+)/?$ index.php?mod=rejected_detail&id=$1 [L,QSA]
# RewriteRule ^rejected/detail/([0-9]+)\.html$ index.php?mod=rejected_detail&id=$1 [L,QSA]

# RSS订阅
# RewriteRule ^rssfeed/([0-9]+)\.html$ index.php?mod=rssfeed&cid=$1 [L,QSA]
# RewriteRule ^rssfeed/(.+)/([0-9]+)\.html$ index.php?mod=rssfeed&cid=$2 [L,QSA]
# RewriteRule ^rssfeed/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=rssfeed&cid=$2&page=$3 [L,QSA]

# 网站地图
# RewriteRule ^sitemap/([0-9]+)\.html$ index.php?mod=sitemap&cid=$1 [L,QSA]

# 网站目录分类页面
# RewriteRule ^webdir/(.+)/([0-9]+)\.html$ index.php?mod=webdir&cid=$2 [L,QSA]
# RewriteRule ^webdir/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=webdir&cid=$2&page=$3 [L,QSA]
# RewriteRule ^webdir/(.+)/([0-9]+)-(instat|outstat|views|ctime)-([0-9]+)\.html$ index.php?mod=webdir&cid=$2&sort=$3&page=$4 [L,QSA]

# 友情链接分类页面
# RewriteRule ^weblink/(.+)/([0-9]+)\.html$ index.php?mod=weblink&cid=$2 [L,QSA]
# RewriteRule ^weblink/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=weblink&cid=$2&page=$3 [L,QSA]
# RewriteRule ^weblink/(.+)/([0-9]+)-(time|id)-([0-9]+)\.html$ index.php?mod=weblink&cid=$2&sort=$3&page=$4 [L,QSA]

# 文章分类页面
# RewriteRule ^article/(.+)/([0-9]+)\.html$ index.php?mod=article&cid=$2 [L,QSA]
# RewriteRule ^article/(.+)/([0-9]+)-([0-9]+)\.html$ index.php?mod=article&cid=$2&page=$3 [L,QSA]

# 网站提交相关
# RewriteRule ^submit/?$ index.php?mod=addurl [L,QSA]
# RewriteRule ^quicksubmit/?$ index.php?mod=quicksubmit [L,QSA]

# 数据统计页面
# RewriteRule ^stats/?$ index.php?mod=datastats [L,QSA]

# 网站图标获取
# RewriteRule ^ico/(.*)\.png$ ico/get.php?url=$1 [L,QSA]

# 网站快照
# RewriteRule ^snapshot(-|/)(.*)$ snapshot.php?site=$2 [L,QSA]

# API接口
# RewriteRule ^api/(.+)$ index.php?mod=api&action=$1 [L,QSA]

# AJAX数据获取
# RewriteRule ^ajaxget/(.+)$ index.php?mod=ajaxget&type=$1 [L,QSA]
# RewriteRule ^getdata/(.+)$ index.php?mod=getdata&type=$1 [L,QSA]

# ===== IIS web.config 版本 =====
# 如果您使用IIS服务器，请创建web.config文件并使用以下规则：

# <?xml version="1.0" encoding="UTF-8"?>
# <configuration>
#     <system.webServer>
#         <rewrite>
#             <rules>
#                 <!-- 基础模块 -->
#                 <rule name="Basic Modules" stopProcessing="true">
#                     <match url="^(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|blacklist|pending|rejected|addurl|quicksubmit|datastats)/?$" />
#                     <action type="Rewrite" url="index.php?mod={R:1}" />
#                 </rule>
#                 
#                 <!-- 最近更新模块 -->
#                 <rule name="Update Days" stopProcessing="true">
#                     <match url="^update/([0-9]+)\.html$" />
#                     <action type="Rewrite" url="index.php?mod=update&amp;days={R:1}" />
#                 </rule>
#                 <rule name="Update Days Page" stopProcessing="true">
#                     <match url="^update/([0-9]+)-([0-9]+)\.html$" />
#                     <action type="Rewrite" url="index.php?mod=update&amp;days={R:1}&amp;page={R:2}" />
#                 </rule>
#                 
#                 <!-- 其他规则类似... -->
#             </rules>
#         </rewrite>
#     </system.webServer>
# </configuration>

# ===== 使用说明 =====
# 1. 对于Nginx服务器：将上面的Nginx规则添加到您的server块中
# 2. 对于Apache服务器：取消注释Apache部分的规则，保存为.htaccess文件
# 3. 对于IIS服务器：使用web.config格式的规则
# 4. 确保您的程序支持所有这些模块和参数
# 5. 测试所有URL是否正常工作

# ===== 新增功能说明 =====
# 本伪静态规则相比原版增加了以下功能：
# - VIP网站列表和详情页面的SEO友好URL
# - 待审核网站的专门URL结构
# - 黑名单网站的分类和详情页面
# - 审核不通过网站的管理页面
# - 网站提交的简化URL
# - 数据统计页面的友好URL
# - API接口的RESTful风格URL
# - 支持更多的排序参数和分页功能
