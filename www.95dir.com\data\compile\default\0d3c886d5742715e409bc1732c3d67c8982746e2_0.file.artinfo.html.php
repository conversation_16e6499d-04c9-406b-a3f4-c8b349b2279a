<?php
/* Smarty version 4.5.5, created on 2025-07-30 15:55:53
  from '/www/wwwroot/www.95dir.com/themes/default/artinfo.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6889d009d0d799_74670657',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '0d3c886d5742715e409bc1732c3d67c8982746e2' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/artinfo.html',
      1 => 1753540028,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:script.html' => 1,
    'file:topbar.html' => 1,
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6889d009d0d799_74670657 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
<head>
<title><?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="robots" content="index,follow" />
<meta name="format-detection" content="telephone=no" />
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="apple-mobile-web-app-title" content="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
 - <?php echo $_smarty_tpl->tpl_vars['options']->value['site_name'];?>
" />
<meta name="theme-color" content="#28a745" />
<link rel="canonical" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=artinfo&aid=<?php echo $_smarty_tpl->tpl_vars['art']->value['art_id'];?>
" />
<link rel="alternate" media="only screen and (max-width: 640px)" href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=artinfo&aid=<?php echo $_smarty_tpl->tpl_vars['art']->value['art_id'];?>
" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/style.css" rel="stylesheet" type="text/css" />

<!-- SEO优化 - 结构化数据：文章 -->
<?php echo '<script'; ?>
 type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "<?php echo strtr((string)$_smarty_tpl->tpl_vars['art']->value['art_title'], array("\\" => "\\\\", "'" => "\\'", "\"" => "\\\"", "\r" => "\\r", 
                       "\n" => "\\n", "</" => "<\/", "<!--" => "<\!--", "<s" => "<\s", "<S" => "<\S",
                       "`" => "\\`", "\${" => "\\\$\{"));?>
",
    "description": "<?php echo strtr((string)$_smarty_tpl->tpl_vars['art']->value['art_intro'], array("\\" => "\\\\", "'" => "\\'", "\"" => "\\\"", "\r" => "\\r", 
                       "\n" => "\\n", "</" => "<\/", "<!--" => "<\!--", "<s" => "<\s", "<S" => "<\S",
                       "`" => "\\`", "\${" => "\\\$\{"));?>
",
    "datePublished": "<?php echo $_smarty_tpl->tpl_vars['art']->value['art_ctime'];?>
",
    "dateModified": "<?php echo $_smarty_tpl->tpl_vars['art']->value['art_ctime'];?>
",
    "author": {
        "@type": "Organization",
        "name": "<?php echo $_smarty_tpl->tpl_vars['options']->value['site_name'];?>
"
    },
    "publisher": {
        "@type": "Organization",
        "name": "<?php echo $_smarty_tpl->tpl_vars['options']->value['site_name'];?>
",
        "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
",
        "logo": {
            "@type": "ImageObject",
            "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
logo.png"
        }
    },
    "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=artinfo&aid=<?php echo $_smarty_tpl->tpl_vars['art']->value['art_id'];?>
"
    },
    "articleSection": "<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
",
    "keywords": "<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
",
    "wordCount": "<?php echo preg_match_all('/[^\s]/u',preg_replace('!<[^>]*?>!', ' ', (string) $_smarty_tpl->tpl_vars['art']->value['art_content']), $tmp);?>
",
    "url": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=artinfo&aid=<?php echo $_smarty_tpl->tpl_vars['art']->value['art_id'];?>
"
}
<?php echo '</script'; ?>
>

<!-- SEO优化 - 结构化数据：面包屑导航 -->
<?php echo '<script'; ?>
 type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "首页",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
"
        },
        {
            "@type": "ListItem",
            "position": 2,
            "name": "站长资讯",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=article"
        },
        {
            "@type": "ListItem",
            "position": 3,
            "name": "<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=article&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
"
        },
        {
            "@type": "ListItem",
            "position": 4,
            "name": "<?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
",
            "item": "<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=artinfo&aid=<?php echo $_smarty_tpl->tpl_vars['art']->value['art_id'];?>
"
        }
    ]
}
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender("file:script.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</head>

<body>
<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<div id="wrapper">
	<?php $_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="artinfo">
            	<h1 class="atitle"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
</h1>
				<div class="aattr">来源：<a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['copy_url'];?>
" target="_blank"><?php echo $_smarty_tpl->tpl_vars['art']->value['copy_from'];?>
</a>　浏览：<?php echo $_smarty_tpl->tpl_vars['art']->value['art_views'];?>
次　时间：<?php echo $_smarty_tpl->tpl_vars['art']->value['art_ctime'];?>
</div>
				<div class="aattr1"><b>简介</b>：<?php echo $_smarty_tpl->tpl_vars['art']->value['art_intro'];?>
</div>
				<div class="content"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_content'];?>
</div>
				<div class="hcatelist">
				    <li><i class="fas fa-tag"></i>标签：
				    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['art_tags']->value, 'tag', false, NULL, 'tags', array (
  'last' => true,
  'iteration' => true,
  'total' => true,
));
$_smarty_tpl->tpl_vars['tag']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['tag']->value) {
$_smarty_tpl->tpl_vars['tag']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['iteration']++;
$_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['last'] = $_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['iteration'] === $_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['total'];
?>
				        <a href="<?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_link'];?>
" title="查看更多关于'<?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_name'];?>
'的文章"><?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_name'];?>
</a><?php if (!(isset($_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['last']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['last'] : null)) {
}?>
				    <?php
}
if ($_smarty_tpl->tpl_vars['tag']->do_else) {
?>
				        暂无标签
				    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				    </li>
				    </div>

				    <!-- SEO内链优化：相关文章和导航 -->
				    <div class="seo-internal-links" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
				        <h4 style="margin: 0 0 12px 0; font-size: 16px; color: #333; font-weight: bold;">
				            <i class="fas fa-link" style="color: #28a745; margin-right: 8px;"></i>相关推荐
				        </h4>
				        <div style="line-height: 2.2; font-size: 14px;">
				            <a href="?mod=article&cid=<?php echo $_smarty_tpl->tpl_vars['cate_id']->value;?>
" title="浏览更多<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
文章" style="color: #28a745; text-decoration: none; margin-right: 15px;">
				                <i class="fas fa-folder" style="margin-right: 5px;"></i>更多<?php echo $_smarty_tpl->tpl_vars['cate_name']->value;?>
文章
				            </a>
				            <a href="?mod=article" title="站长资讯" style="color: #28a745; text-decoration: none; margin-right: 15px;">
				                <i class="fas fa-newspaper" style="margin-right: 5px;"></i>站长资讯
				            </a>
				            <a href="?mod=webdir" title="网站目录" style="color: #28a745; text-decoration: none; margin-right: 15px;">
				                <i class="fas fa-sitemap" style="margin-right: 5px;"></i>网站目录
				            </a>
				            <a href="?mod=update" title="最新收录" style="color: #28a745; text-decoration: none; margin-right: 15px;">
				                <i class="fas fa-clock" style="margin-right: 5px;"></i>最新收录
				            </a>
				            <a href="?mod=vip_list" title="VIP网站" style="color: #28a745; text-decoration: none;">
				                <i class="fas fa-crown" style="margin-right: 5px;"></i>VIP网站
				            </a>
				        </div>
				    </div>

				    <div class="text-xs text-muted"><div><span>©</span> 版权声明</div><div class="posts-copyright"><div><br><fieldset style="border:1px dashed #008CFF;padding:10px;border-radius:8px;line-height: 2em;color: #6D6D6D"><legend align="center" style="color:#FFFFFF;width:200px;text-align:center;background-color:#008CFF;font-size: 14px;border-radius: 5px">95分类目录 - 版权声明</legend>1、本主题所有言论和图片纯属会员个人意见，与本站立场无关。<br> 2、本站所有主题由该文章作者发表，该文章作者与<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>享有文章相关版权。<br> 3、其他单位或个人使用、转载或引用本文时必须同时征得该文章作者和<a href="https://www.95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>的同意。<br> 4、文章作者须承担一切因本文发表而直接或间接导致的民事或刑事法律责任。<br> 5、本帖部分内容转载自其它媒体，但并不代表本站赞同其观点和对其真实性负责。<br> 6、如本帖侵犯到任何版权问题，请立即告知本站，本站将及时予与删除并致以最深的歉意。<br> 7、<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>管理员有权不事先通知发贴者而删除本文。</fieldset><br></div></div></div>
				    <!-- 打赏按钮 -->
				    <div class="donate-button-container">
<button onclick="showDonatePopup()">打赏支持</button>
<div class="fenxiang"><div class="social-share"></div></div>
</div><!-- 打赏弹窗 -->
<div id="donate-popup" style="display:none;">
<div class="donate-popup-content">
<span class="close" onclick="closeDonatePopup()">&times;</span>
<h3>您的赞助更新的动力</h3>
<div class="donate-qr-codes">
<div>
<h4>微信打赏</h4>
<img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206265.png" alt="WeChat QR Code">
</div>
<div>
<h4>支付宝打赏</h4>
<img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206984.png" alt="Alipay QR Code">
</div>
</div>
</div>
</div>
                <ul class="prevnext">
                	<li>上一篇： <?php if (!empty($_smarty_tpl->tpl_vars['prev']->value)) {?><a href="<?php echo $_smarty_tpl->tpl_vars['prev']->value['art_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['prev']->value['art_title'];?>
</a><?php } else { ?>没有了<?php }?></li>
                    <li>下一篇： <?php if (!empty($_smarty_tpl->tpl_vars['next']->value)) {?><a href="<?php echo $_smarty_tpl->tpl_vars['next']->value['art_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['next']->value['art_title'];?>
</a><?php } else { ?>没有了<?php }?></li>
                </ul>
            </div>
            <div class="blank10"></div>
        </div>
        <div id="mainbox-right">
        	<!--<div class="ad250x250"><?php echo get_adcode(7);?>
</div>-->
            <div class="blank10"></div>
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_articles(0,10), 'art');
$_smarty_tpl->tpl_vars['art']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['art']->value) {
$_smarty_tpl->tpl_vars['art']->do_else = false;
?>
                	<li><a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
</a></li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,5,false,true), 'best');
$_smarty_tpl->tpl_vars['best']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['best']->value) {
$_smarty_tpl->tpl_vars['best']->do_else = false;
?>
                   	<li><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
</a></strong><p><?php echo $_smarty_tpl->tpl_vars['best']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['best']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_url'];?>
</a></address></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        </div>
    </div>
    <?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</div>
</body>
</html><?php }
}
