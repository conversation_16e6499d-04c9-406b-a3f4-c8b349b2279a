<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
            <div id="listbox" class="clearfix">
            	<h2>{#$pagename#}</h2>
            	<table border="0" cellpadding="0" cellspacing="1" width="100%" class="weblink">
                	<tr>
                    	<th>交易方式</th>
                    	<th>网站名称</th>
                        <th>网站域名</th>
                        <th>所属分类</th>
                        <th>百度收录</th>
                        <th>必应收录</th>
                        <th>360收录</th>
                        <th>搜狗收录</th>
                        <th>更新时间</th>
                        <th>联系站长</th>
                    </tr>
                    {#foreach from=$weblinks item=item#}
                    <tr style="text-align: center;">
                    	<td>{#$item.deal_type#}</td>
                    	<td><a href="{#$item.web_link#}">{#$item.link_name#}</a></td>
                        <td>{#$item.web_url#}</td>
                        <td>{#$item.cate_name#}</td>
                        <td>{#$item.web_grank#}</td>
                        <td>{#$item.web_brank#}</td>
                        <td>{#$item.web_srank#}</td>
                        <td>{#$item.web_arank#}</td>
                        <td>{#$item.link_time#}</td>
                        <td><a href="tencent://message/?uin={#$item.user_qq#}&site={#$item.web_url#}&menu=yes"><img src="http://wpa.qq.com/pa?p=1:{#$item.user_qq#}:4" title="{#$item.user_qq#}"/></a></td>
                    </tr>
                    {#/foreach#}
                </table>
                <div class="showpage">{#$showpage#}</div>
            </div>
        </div>
        <div id="mainbox-right">
        	<!--<div class="ad250x250">{#get_adcode(7)#}</div>
            <div class="blank10"></div>-->
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li><a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=quick#}
                   	<li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}" /></a><strong><a href="{#$quick.web_link#}" title="{#$best.web_name#}">{#$quick.web_name#}</a></strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
            
            <div class="blank10"></div>
            
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>
</body>
</html>