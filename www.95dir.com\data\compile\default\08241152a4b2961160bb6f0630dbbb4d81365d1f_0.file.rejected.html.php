<?php
/* Smarty version 4.5.5, created on 2025-07-30 15:25:54
  from '/www/wwwroot/www.95dir.com/themes/default/rejected.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6889c902f2d0f9_36878612',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '08241152a4b2961160bb6f0630dbbb4d81365d1f' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/rejected.html',
      1 => 1753625915,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:script.html' => 1,
    'file:topbar.html' => 1,
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6889c902f2d0f9_36878612 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
<head>
<title><?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/scripts/common.js"><?php echo '</script'; ?>
>
<?php $_smarty_tpl->_subTemplateRender("file:script.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

</head>

<body>
<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<div id="wrapper">
	<?php $_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="subcate" class="clearfix">
            	<h3><?php echo $_smarty_tpl->tpl_vars['current_cate_name']->value;?>
</h3>
                <ul class="scatelist">
                	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['categories']->value, 'sub');
$_smarty_tpl->tpl_vars['sub']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['sub']->value) {
$_smarty_tpl->tpl_vars['sub']->do_else = false;
?>
                    <li><a href="?mod=rejected&cid=<?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_id'];?>
"<?php if ($_smarty_tpl->tpl_vars['sub']->value['cate_id'] == $_smarty_tpl->tpl_vars['current_cate_id']->value) {?> class="current"<?php }?>><?php echo $_smarty_tpl->tpl_vars['sub']->value['cate_name'];?>
</a></li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>

            <div class="blank10"></div>
            <div id="listbox" class="clearfix">
            	<h2><?php echo $_smarty_tpl->tpl_vars['current_cate_name']->value;?>
</h2>
            	<ul class="sitelist"><div class="rejected-notice">
                    <strong>隐私保护说明：</strong>为保护网站隐私，审核不通过期间不显示具体网址。网站管理员可根据反馈进行整改后重新提交。
                </div>
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'w', false, NULL, 'list', array (
));
$_smarty_tpl->tpl_vars['w']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['w']->value) {
$_smarty_tpl->tpl_vars['w']->do_else = false;
?>
                	<li><img src="themes/default/skin/wait.png" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
" class="thumb" style="filter: hue-rotate(30deg) saturate(1.2);" /><div class="info"><h3><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
</a> <span style="background:#f39c12;color:white;padding:2px 6px;border-radius:3px;font-size:10px;">审核不通过</span></h3><p><?php echo $_smarty_tpl->tpl_vars['w']->value['web_intro'];?>
</p><address>
                	    <!-- 隐私保护说明 -->

                <style>
/* 审核不通过页面专用样式 */
.rejected-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-left: 4px solid #f39c12;
    border-radius: 5px;
    padding: 12px 15px;
    margin-bottom: 15px;
    color: #856404;
    font-size: 13px;
}
</style>
  <!-- 隐私保护：不显示网址 -->
  <span style="color:#f39c12;font-size:12px;">审核不通过</span>
  <?php if ($_smarty_tpl->tpl_vars['w']->value['web_reject_reason']) {?><br><span style="color:#856404;font-size:11px;">原因：<?php echo $_smarty_tpl->tpl_vars['w']->value['web_reject_reason'];?>
</span><?php }?>
  - <?php echo $_smarty_tpl->tpl_vars['w']->value['web_ctime'];?>
 -
  <a href="javascript:;" class="addfav"
     onClick="addfav(<?php echo $_smarty_tpl->tpl_vars['w']->value['web_id'];?>
)" title="点击收藏">收藏</a>
</address></div></li>
                	<?php
}
if ($_smarty_tpl->tpl_vars['w']->do_else) {
?>
                	<li>该目录下无任何审核不通过内容！</li>
                	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				</ul>
            	<div class="showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
            </div>
        </div>

        <div id="mainbox-right">

            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,true), 'quick');
$_smarty_tpl->tpl_vars['quick']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->do_else = false;
?>
                   	<li><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a></strong><p><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_url'];?>
</a></address></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>

            <div class="blank10"></div>

            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,5,false,true), 'best');
$_smarty_tpl->tpl_vars['best']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['best']->value) {
$_smarty_tpl->tpl_vars['best']->do_else = false;
?>
                   	<li><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
</a></strong><p><?php echo $_smarty_tpl->tpl_vars['best']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['best']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_url'];?>
</a></address></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        	<!--<div class="ad250x250"><?php echo get_adcode(7);?>
</div>-->
            <div class="blank10"></div>
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_articles(0,14), 'art');
$_smarty_tpl->tpl_vars['art']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['art']->value) {
$_smarty_tpl->tpl_vars['art']->do_else = false;
?>
                	<li><a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
</a></li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
        </div>
    </div>
    <?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</div>

</body>
</html>
<?php }
}
