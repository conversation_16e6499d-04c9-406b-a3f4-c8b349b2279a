<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/default/skin/svg-fix.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
<script type="text/javascript" src="{#$site_root#}themes/default/skin/svg-fix.js"></script>

</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
    		<!-- 待审核状态横幅 -->
    		<div style="background: linear-gradient(45deg, #ff6600, #ff8533); color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; text-align: center; position: relative; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
    			<img src="themes/default/skin/wait.png" alt="待审核" style="position: absolute; top: -10px; right: -10px; width: 80px; height: 80px; opacity: 0.3; transform: rotate(15deg);" />
    			<h2 style="margin: 0; font-size: 18px; font-weight: bold;">🔍 网站审核中</h2>
    			<p style="margin: 5px 0 0 0; font-size: 14px;">该网站正在进行人工审核，审核通过后将正式收录</p>
    		</div>
        	<div id="siteinfo">
            	<h1 class="wtitle">
                    <span style="float: right; margin-top: 5px;">
                        <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#ff6600;border-radius:1em;padding:5px 15px;">
                            待审核
                        </span>
                    </span>
                    <em style="color: #f60;">{#$web.web_name#} - 待审核网站</em>
                </h1>

				<ul class="wdata">
                    <li class="line"><em style="color: #ff6600;">待审核</em>审核状态</li>
                    <li class="line"><em style="color: #f00;">{#$web.web_views#}</em>人气指数</li>
                    <li class="line"><em style="color: #666;">{#$web.web_grank#}</em>百度收录量</li>
                    <li class="line"><em style="color: #666;">{#$web.web_brank#}</em>必应收录量</li>
                    <li class="line"><em style="color: #666;">{#$web.web_srank#}</em>360收录量</li>
                    <li class="line"><em style="color: #666;">{#$web.web_arank#}</em>搜狗收录量</li>
                    <li class="line"><em style="color: #666;">隐私保护</em>网站状态</li>
                    <li class="line"><em style="color: #666;">-</em>响应时间</li>
                    <li class="line"><em>{#$web.web_instat#}</em>入站次数</li>
                    <li class="line"><em>{#$web.web_outstat#}</em>出站次数</li>
                    <li class="line"><em>{#$web.web_ctime#}</em>提交日期</li>
                    <li class="line"><em style="color: #f60;">{#$web.web_utime#}</em>更新日期</li>
                </ul>

				<div class="clearfix params">
					<div style="position: relative; display: inline-block;">
						<img src="{#$web.web_pic#}" width="130" height="110" alt="{#$web.web_name#}" class="wthumb" />
						<img src="themes/default/skin/wait.png" alt="待审核" style="position: absolute; top: 5px; right: 5px; width: 60px; height: 60px; opacity: 0.9; z-index: 10;" />
					</div>
					<ul class="siteitem">
						<li>
                            <strong>网站地址：</strong>
                            <span style="color:#f00;font-size:12px;">隐私保护：网址信息已隐藏</span>

                            <!-- 隐私保护说明按钮 -->
                            <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#dc3545;border-radius:1em;float:right;width:80px;">
                                隐私保护
                            </span>
                        </li>
            			<li><strong>服务器IP：</strong>{#$web.web_ip#}</li>
                        <li><strong>网站描述：</strong><span style="line-height: 23px;">{#$web.web_intro#}</span></li>
                        <li><strong>综合权重：</strong><span style="color: #666;">待审核通过后显示</span></li>
                        <li><strong>备案信息：</strong><span style="color: #666;">待审核通过后显示</span></li>
                        <li><strong>联系站长：</strong><span style="color: #666;">待审核通过后显示</span></li>
                        <li><strong>TAG标签：</strong>{#foreach from=$web_tags item=item#}<a href="javascript:void(0)" style="background: #e9ecef; color: #495057; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-right: 5px; text-decoration: none;">{#$item.tag_name#}</a>{#/foreach#}</li>
                        <li>{#get_adcode(1)#}</li>
                        <li><strong>审核状态：</strong><span style="color: #ff6600; font-weight: bold;">该网站正在审核中，审核通过后将开放访问</span></li>
                        <li><strong>本页地址：</strong><a href="{#$site_url#}pendingdetail/{#$web.web_id#}.html">{#$site_url#}pendingdetail/{#$web.web_id#}.html</a></li>
					</ul>
				</div>
            </div>

            <div class="blank10"></div>

            <!-- 隐私保护说明 -->
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-left: 4px solid #ffc107; border-radius: 5px; padding: 12px 15px; margin-bottom: 15px; color: #856404; font-size: 13px;">
                <strong>隐私保护说明：</strong>该网站正在审核中，为保护网站隐私，暂不显示具体网址、服务器信息和访问入口。审核通过后将正式收录并提供完整信息。
            </div>

            <div class="web_ai_intro">关于【{#$web.web_name#}】的详细介绍</div>
            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
        	    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
        	        <img src="themes/default/skin/wait.png" width="200" height="150" alt="{#$web.web_name#} - 待审核" style="opacity: 0.8; filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));" />
        	        <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">网站正在审核中，请耐心等待...</p>
        	    </div>
        	    <div class="blank10"></div>
				{#if $web.web_ai_intro#}
				{#$web.web_ai_intro#}
				{#else#}
				<p>该网站正在审核中，AI简介将在审核通过后生成。目前仅显示基本的网站描述信息。</p>
				{#/if#}
			</div>

			<div class="blank10"></div>
            <div class="web_ai_intro">注意事项：凡违反中国国家法律法规的网站，95分类目录一律不给予收录。</div>
            <div class="blank10"></div>
			<div id="relsite" class="clearfix">
                <p><strong>{#$web.web_name#}</strong>于{#$web.web_ctime#}被提交到{#$cate_name#}分类目录，目前正在审核中。相关信息来自网站提交者提供！由于网站内容动态属性，时刻在变动，本站无法保证该网站的合法性以及内容真实可靠！请大家查阅时，谨慎选择、自辩真伪，感谢您的理解与支持。如果您在了解（{#$web.web_name#}）时发现：信息不实或网站存在非法等相关内容，请及时联系我们处理，我们将在第一时间处理。</p>
            </div>

            <!-- 隐私保护警告 -->
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; font-size: 13px;">
                <strong>🔒 隐私保护提醒：</strong>
                <ul style="margin: 5px 0 0 20px; padding: 0;">
                    <li>该网站正在审核过程中，网址信息已被隐藏</li>
                    <li>审核期间不提供任何访问入口</li>
                    <li>审核通过后将正式收录并开放访问</li>
                    <li>如有疑问请联系网站管理员</li>
                </ul>
            </div>

            <!-- 上一站下一站导航 -->
            <div class="website-navigation" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
            	<div style="display: flex; justify-content: space-between; align-items: center;">
            		<div class="prev-website" style="flex: 1; text-align: left;">
            			{#if $prev_website#}
            				<a href="{#$prev_website.web_link#}" style="color: #007bff; text-decoration: none; font-size: 14px;"
            				   onmouseover="this.style.color='#0056b3'" onmouseout="this.style.color='#007bff'">
            					<i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
            					上一站：{#$prev_website.web_name#}
            				</a>
            			{#else#}
            				<span style="color: #6c757d; font-size: 14px;">
            					<i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
            					上一站：暂无
            				</span>
            			{#/if#}
            		</div>

            		<div class="navigation-center" style="flex: 0 0 auto; margin: 0 20px;">
            			<span style="color: #6c757d; font-size: 12px;">待审核导航</span>
            		</div>

            		<div class="next-website" style="flex: 1; text-align: right;">
            			{#if $next_website#}
            				<a href="{#$next_website.web_link#}" style="color: #007bff; text-decoration: none; font-size: 14px;"
            				   onmouseover="this.style.color='#0056b3'" onmouseout="this.style.color='#007bff'">
            					下一站：{#$next_website.web_name#}
            					<i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
            				</a>
            			{#else#}
            				<span style="color: #6c757d; font-size: 14px;">
            					下一站：暂无
            					<i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
            				</span>
            			{#/if#}
            		</div>
            	</div>
            </div>

            <div class="blank10"></div>

            <!-- 操作按钮 -->
            <div style="display: flex; justify-content: center; align-items: center; gap: 15px; margin: 20px 0; flex-wrap: wrap;">
                <button onclick="showDonatePopup()" style="background: #ff6600; color: white; padding: 10px 20px; border: none; border-radius: 3px; font-size: 14px; cursor: pointer; text-decoration: none; display: inline-block;">我要快审</button>
                <a href="?mod=pending" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; font-size: 14px; display: inline-block;">返回待审核列表</a>
                <a href="?mod=pending&cid={#$web.cate_id#}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; font-size: 14px; display: inline-block;">查看同分类</a>
                <button onclick="addfav({#$web.web_id#})" title="点击收藏" style="background: #ffc107; color: #212529; padding: 10px 20px; border: none; border-radius: 3px; font-size: 14px; cursor: pointer; display: inline-block;">收藏网站</button>
            </div>

            <!-- 统一弹窗 -->
            <div id="donate-popup" style="display:none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
                <div class="donate-popup-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 500px; width: 90%;">
                    <span class="close" onclick="closeDonatePopup()" style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #999;">&times;</span>
                    <h3 style="color: #4A90E2; font-family: 'Arial', sans-serif; font-size: 22px; text-align: center; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); margin-top: 0;">
                        推荐服务价格表
                    </h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0; line-height: 1.6; text-align: center;">
                            <strong style="color: #28a745;">10元上推荐位</strong> - 首页推荐展示<br>
                            <strong style="color: #E94E77;">VIP直链席位30元/每年</strong> - 顶部VIP位置<br>
                            <strong style="color: #ff6600;">5元快审服务</strong> - 1-3个工作日审核
                        </p>
                    </div>
                    <p style="text-align: center; margin: 15px 0; color: #666;">
                        备注格式：<strong style="color: #F39C12;">推荐/vip/快审+网址</strong>
                    </p>
                    <div class="donate-qr-codes" style="display: flex; justify-content: space-around; margin: 20px 0;">
                        <div style="text-align: center;">
                            <h4>微信支付</h4>
                            <img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206265.png" alt="WeChat QR Code" style="width: 150px; height: 150px;">
                        </div>
                        <div style="text-align: center;">
                            <h4>支付宝支付</h4>
                            <img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206984.png" alt="Alipay QR Code" style="width: 150px; height: 150px;">
                        </div>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
                        <h4 style="margin-top: 0; color: #333;">服务说明：</h4>
                        <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>推荐位：展示在首页推荐区域</li>
                            <li>VIP位：展示在顶部VIP推广区</li>
                            <li>快审：1-3个工作日完成审核</li>
                            <li>付款后请联系客服提供网站信息</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="blank10"></div>

            <!-- 版权声明 -->
            <div class="text-xs text-muted">
                <div><span>©</span> 版权声明</div>
                <div class="posts-copyright">
                    <div><br>
                        <fieldset style="border:1px dashed #008CFF;padding:10px;border-radius:8px;line-height: 2em;color: #6D6D6D">
                            <legend align="center" style="color:#FFFFFF;width:200px;text-align:center;background-color:#008CFF;font-size: 14px;border-radius: 5px">95分类目录 - 版权声明</legend>
                            1、本主题所有言论和图片纯属会员个人意见，与本站立场无关。<br>
                            2、本站所有主题由该文章作者发表，该文章作者与<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>享有文章相关版权。<br>
                            3、其他单位或个人使用、转载或引用本文时必须同时征得该文章作者和<a href="https://www.95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>的同意。<br>
                            4、文章作者须承担一切因本文发表而直接或间接导致的民事或刑事法律责任。<br>
                            5、本帖部分内容转载自其它媒体，但并不代表本站赞同其观点和对其真实性负责。<br>
                            6、如本帖侵犯到任何版权问题，请立即告知本站，本站将及时予与删除并致以最深的歉意。<br>
                            7、<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>管理员有权不事先通知发贴者而删除本文。
                        </fieldset><br>
                    </div>
                </div>
            </div>

            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
            	<h2>同分类待审核站点</h2>
               	<ul class="rellist">
              		{#foreach from=$related_pending item=rel#}
               		<li><a href="{#$site_url#}pendingdetail/{#$rel.web_id#}.html"><img src="{#$rel.web_pic#}" width="100" height="80" alt="{#$rel.web_name#}" /><strong>{#$rel.web_name#}</strong></a></li>
               		{#foreachelse#}
               		<li>暂无其他同分类待审核网站</li>
               		{#/foreach#}
              	</ul>
            </div>

            <div class="blank10"></div>
            <div id="relsite" class="clearfix">
            	<h2>本类排行榜</h2>
               	<ul class="rellist">
              		{#foreach from=get_websites($web.cate_id, 10, false, false, 'views') item=hot name=hot_website#}
            <li><a href="{#$hot.web_link#}"><img src="{#$hot.web_pic#}" width="100" height="80" alt="{#$hot.web_name#}" /><strong>{#$hot.web_name#}</strong></a></li>
            {#/foreach#}
              	</ul>
            </div>
        </div>

        <div id="mainbox-right">
        	<!--<div class="ad250x250">{#get_adcode(7)#}</div>
            <div class="blank10"></div>-->

            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 10, 'views') item=quick#}
                   	<li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}" /></a><strong><a href="{#$quick.web_link#}" title="{#$quick.web_name#}">{#$quick.web_name#}</a></strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>

            <div class="blank10"></div>

            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li>[<em><a href="{#$art.cate_link#}" title="{#$art.cate_name#}">{#$art.cate_name#}</a></em>]<a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>
            <div id="bestart">
                <h3>最新收录</h3>
            <ul class="artlist_b">
                    {#foreach from=get_websites(0, 8) item=new#}
					<li data-number="{#$idx+1#}">[<em><a href="{#$new.cate_link#}" title="{#$new.cate_name#}">{#$new.cate_name#}</a></em>]<a href="{#$new.web_link#}" title="{#$new.web_name#}">{#$new.web_name#}</a><span>{#$new.web_ctime#}</span></li>
                   	{#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>
            <div id="bestweb">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="/go.php?url=http://{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>

<script>
// 统一弹窗功能
function showDonatePopup() {
    document.getElementById('donate-popup').style.display = 'block';
}

function closeDonatePopup() {
    document.getElementById('donate-popup').style.display = 'none';
}

// 点击弹窗外部关闭
document.getElementById('donate-popup').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDonatePopup();
    }
});

// ESC键关闭弹窗
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDonatePopup();
    }
});
</script>

</body>
</html>
