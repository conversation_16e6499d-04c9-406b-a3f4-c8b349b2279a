# 按照您现有风格增加的伪静态规则

# 基础模块（增加新模块）
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|blacklist|pending|rejected|addurl|quicksubmit|datastats|vip_list|vip_detail|pending_detail|blacklist_detail|rejected_detail)(/?)$ /index.php?mod=$1;

# 最近更新模块
rewrite ^/update/(\d+)\.html$ /index.php?mod=update&days=$1;
rewrite ^/update/(\d+)-(\d+)\.html$ /index.php?mod=update&days=$1&page=$2;

# 数据归档模块
rewrite ^/archives/(\d+)\.html$ /index.php?mod=archives&date=$1;
rewrite ^/archives/(\d+)-(\d+)\.html$ /index.php?mod=archives&date=$1&page=$2;

# 站内搜索模块
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 快速搜索
rewrite ^/(br|pr)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/(br|pr)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 网站详情页
rewrite ^/view/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo-(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/site/(\d+)-(.+)(/?)\.html$ /index.php?mod=siteinfo&wid=$1;

# 文章详情页
rewrite ^/artinfo/(\d+)\.html$ /index.php?mod=artinfo&aid=$1;

# 友情链接详情页
rewrite ^/linkinfo/(\d+)\.html$ /index.php?mod=linkinfo&lid=$1;

# 自定义单页
rewrite ^/diypage/(\d+)\.html$ /index.php?mod=diypage&pid=$1;

# RSS订阅
rewrite ^/rssfeed/(\d+)\.html$ /index.php?mod=rssfeed&cid=$1;

# 网站地图
rewrite ^/sitemap/(\d+)\.html$ /index.php?mod=sitemap&cid=$1;

# 网站目录分类页面
rewrite ^/webdir/(.+)/(\d+)\.html$ /index.php?mod=webdir&cid=$2;
rewrite ^/webdir/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3;
rewrite ^/webdir/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&sort=$3&page=$4;

# 友情链接分类页面
rewrite ^/weblink/(.+)/(\d+)\.html$ /index.php?mod=weblink&cid=$2;
rewrite ^/weblink/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3;
rewrite ^/weblink/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&sort=$3&page=$4;

# 文章分类页面
rewrite ^/article/(.+)/(\d+)\.html$ /index.php?mod=article&cid=$2;
rewrite ^/article/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3;

# RSS订阅分类页面
rewrite ^/rssfeed/(.+)/(\d+)\.html$ /index.php?mod=rssfeed&cid=$2;
rewrite ^/rssfeed/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rssfeed&cid=$2&page=$3;

# ===== 新增：VIP网站相关（按现有风格） =====
rewrite ^/vip/(.+)/(\d+)\.html$ /index.php?mod=vip_list&cid=$2;
rewrite ^/vip/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=vip_list&cid=$2&page=$3;
rewrite ^/vipdetail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1;
# VIP详情页的多种格式支持
rewrite ^/vip/detail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1;

# ===== 新增：待审核网站相关（按现有风格） =====
rewrite ^/pending/(.+)/(\d+)\.html$ /index.php?mod=pending&cid=$2;
rewrite ^/pending/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=pending&cid=$2&page=$3;
rewrite ^/pendingdetail/(\d+)\.html$ /index.php?mod=pending_detail&id=$1;

# ===== 新增：黑名单网站相关（按现有风格） =====
rewrite ^/blacklist/(.+)/(\d+)\.html$ /index.php?mod=blacklist&category=$2;
rewrite ^/blacklist/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=blacklist&category=$2&page=$3;
rewrite ^/blacklistdetail/(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1;

# ===== 新增：审核不通过网站相关（按现有风格） =====
rewrite ^/rejected/(.+)/(\d+)\.html$ /index.php?mod=rejected&cid=$2;
rewrite ^/rejected/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rejected&cid=$2&page=$3;
rewrite ^/rejecteddetail/(\d+)\.html$ /index.php?mod=rejected_detail&id=$1;

# 网站图标获取
rewrite ^/ico/(.*)\.png$ /ico/get.php?url=$1;

# 网站快照
rewrite ^/snapshot(-|\/)(.*)$ /snapshot.php?site=$2;

# ===== URL示例说明 =====
# VIP网站：
# /vip/ -> ?mod=vip_list (VIP列表首页)
# /vip/科技/1.html -> ?mod=vip_list&cid=1 (VIP分类页面)
# /vip/科技/1-2.html -> ?mod=vip_list&cid=1&page=2 (VIP分类第2页)
# /vipdetail/123.html -> ?mod=vip_detail&id=123 (VIP详情页)

# 待审核网站：
# /pending/ -> ?mod=pending (待审核列表首页)
# /pending/科技/1.html -> ?mod=pending&cid=1 (待审核分类页面)
# /pending/科技/1-2.html -> ?mod=pending&cid=1&page=2 (待审核分类第2页)
# /pendingdetail/123.html -> ?mod=pending_detail&id=123 (待审核详情页)

# 黑名单网站：
# /blacklist/ -> ?mod=blacklist (黑名单列表首页)
# /blacklist/违规/1.html -> ?mod=blacklist&category=1 (黑名单分类页面)
# /blacklist/违规/1-2.html -> ?mod=blacklist&category=1&page=2 (黑名单分类第2页)
# /blacklistdetail/123.html -> ?mod=blacklist_detail&id=123 (黑名单详情页)

# 审核不通过网站：
# /rejected/ -> ?mod=rejected (审核不通过列表首页)
# /rejected/科技/1.html -> ?mod=rejected&cid=1 (审核不通过分类页面)
# /rejected/科技/1-2.html -> ?mod=rejected&cid=1&page=2 (审核不通过分类第2页)
# /rejecteddetail/123.html -> ?mod=rejected_detail&id=123 (审核不通过详情页)

# ===== 注意事项 =====
# 1. 完全按照您现有的伪静态风格编写
# 2. 保持与webdir、article、weblink相同的URL结构
# 3. 分类页面格式：/模块名/分类名/分类ID.html
# 4. 分页格式：/模块名/分类名/分类ID-页码.html
# 5. 详情页格式：/模块名detail/ID.html
# 6. 黑名单使用category参数，其他使用cid参数（与程序代码一致）
