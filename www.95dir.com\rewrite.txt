# ========================================
# 95分类目录 Nginx 伪静态规则
# 适用于 95DIR-v3.0 系统
# ========================================

# 基础模块（首页、分类浏览、数据归档、最近更新、排行榜、意见反馈等）
if ($uri ~ "^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|addurl|quicksubmit|datastats)(\.html|/?|)$") {
    rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|addurl|quicksubmit|datastats)(\.html|/?|)$ /index.php?mod=$1 last;
}

# VIP网站相关 - 虚拟目录结构
# VIP首页
if ($uri ~ "^/vip/?$") {
    rewrite ^/vip/?$ /index.php?mod=vip_list last;
}
# VIP列表页面
if ($uri ~ "^/vip/list/?$") {
    rewrite ^/vip/list/?$ /index.php?mod=vip_list last;
}
if ($uri ~ "^/vip/list/(\d+)/?$") {
    rewrite ^/vip/list/(\d+)/?$ /index.php?mod=vip_list&page=$1 last;
}
# VIP分类页面
if ($uri ~ "^/vip/category/(\d+)/?$") {
    rewrite ^/vip/category/(\d+)/?$ /index.php?mod=vip_list&cid=$1 last;
}
if ($uri ~ "^/vip/category/(\d+)/(\d+)/?$") {
    rewrite ^/vip/category/(\d+)/(\d+)/?$ /index.php?mod=vip_list&cid=$1&page=$2 last;
}
# VIP详情页面
if ($uri ~ "^/vip/detail/(\d+)/?$") {
    rewrite ^/vip/detail/(\d+)/?$ /index.php?mod=vip_detail&id=$1 last;
}

# 黑名单网站相关 - 虚拟目录结构
# 黑名单首页
if ($uri ~ "^/blacklist/?$") {
    rewrite ^/blacklist/?$ /index.php?mod=blacklist last;
}
# 黑名单分页
if ($uri ~ "^/blacklist/(\d+)/?$") {
    rewrite ^/blacklist/(\d+)/?$ /index.php?mod=blacklist&page=$1 last;
}
# 黑名单分类页面
if ($uri ~ "^/blacklist/category/(\d+)/?$") {
    rewrite ^/blacklist/category/(\d+)/?$ /index.php?mod=blacklist&category=$1 last;
}
if ($uri ~ "^/blacklist/category/(\d+)/(\d+)/?$") {
    rewrite ^/blacklist/category/(\d+)/(\d+)/?$ /index.php?mod=blacklist&category=$1&page=$2 last;
}
# 黑名单详情页面
if ($uri ~ "^/blacklist/detail/(\d+)/?$") {
    rewrite ^/blacklist/detail/(\d+)/?$ /index.php?mod=blacklist_detail&id=$1 last;
}

# 待审核网站相关 - 虚拟目录结构
# 待审核首页
if ($uri ~ "^/pending/?$") {
    rewrite ^/pending/?$ /index.php?mod=pending last;
}
# 待审核分页
if ($uri ~ "^/pending/(\d+)/?$") {
    rewrite ^/pending/(\d+)/?$ /index.php?mod=pending&page=$1 last;
}
# 待审核分类页面
if ($uri ~ "^/pending/category/(\d+)/?$") {
    rewrite ^/pending/category/(\d+)/?$ /index.php?mod=pending&cid=$1 last;
}
if ($uri ~ "^/pending/category/(\d+)/(\d+)/?$") {
    rewrite ^/pending/category/(\d+)/(\d+)/?$ /index.php?mod=pending&cid=$1&page=$2 last;
}
# 待审核详情页面
if ($uri ~ "^/pending/detail/(\d+)/?$") {
    rewrite ^/pending/detail/(\d+)/?$ /index.php?mod=pending_detail&id=$1 last;
}

# 拒绝网站相关 - 虚拟目录结构
# 拒绝首页
if ($uri ~ "^/rejected/?$") {
    rewrite ^/rejected/?$ /index.php?mod=rejected last;
}
# 拒绝分页
if ($uri ~ "^/rejected/(\d+)/?$") {
    rewrite ^/rejected/(\d+)/?$ /index.php?mod=rejected&page=$1 last;
}
# 拒绝分类页面
if ($uri ~ "^/rejected/category/(\d+)/?$") {
    rewrite ^/rejected/category/(\d+)/?$ /index.php?mod=rejected&cid=$1 last;
}
if ($uri ~ "^/rejected/category/(\d+)/(\d+)/?$") {
    rewrite ^/rejected/category/(\d+)/(\d+)/?$ /index.php?mod=rejected&cid=$1&page=$2 last;
}
# 拒绝详情页面
if ($uri ~ "^/rejected/detail/(\d+)/?$") {
    rewrite ^/rejected/detail/(\d+)/?$ /index.php?mod=rejected_detail&id=$1 last;
}

# 最近更新模块 - 支持天数和分页
if ($uri ~ "^/update(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/update(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=update&days=$2&page=$4 last;
}
if ($uri ~ "^/update(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/update(-|/)(\d+)(\.html|/?|)$ /index.php?mod=update&days=$2 last;
}

# 数据归档模块 - 支持日期和分页
if ($uri ~ "^/archives(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/archives(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=archives&date=$2&page=$4 last;
}
if ($uri ~ "^/archives(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/archives(-|/)(\d+)(\.html|/?|)$ /index.php?mod=archives&date=$2 last;
}

# 站内搜索 - 支持多种搜索类型
if ($uri ~ "^/search(-|/)(name|url|tags|intro|baidu|br|pr|sr|article)(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/search(-|/)(name|url|tags|intro|baidu|br|pr|sr|article)(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=search&type=$2&query=$4&page=$6 last;
}
if ($uri ~ "^/search(-|/)(name|url|tags|intro|baidu|br|pr|sr|article)(-|/)(.+?)(\.html|/?|)$") {
    rewrite ^/search(-|/)(name|url|tags|intro|baidu|br|pr|sr|article)(-|/)(.+?)(\.html|/?|)$ /index.php?mod=search&type=$2&query=$4 last;
}

# 详细页面 - 网站详细、文章详细、链接详细、自定义页面
if ($uri ~ "^/(siteinfo|artinfo|linkinfo|diypage)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/(siteinfo|artinfo|linkinfo|diypage)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=$1&wid=$3 last;
}

# 伪静态详细页面 - 兼容原有URL结构
# VIP详细页面 - 使用id参数，支持伪静态URL
if ($uri ~ "^/vipdetail(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/vipdetail(-|/)(\d+)(\.html|/?|)$ /index.php?mod=vip_detail&id=$2 last;
}

# 黑名单详细页面 - 使用id参数，支持伪静态URL
if ($uri ~ "^/blacklistdetail(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/blacklistdetail(-|/)(\d+)(\.html|/?|)$ /index.php?mod=blacklist_detail&id=$2 last;
}

# 待审核详细页面 - 使用id参数，支持伪静态URL
if ($uri ~ "^/pendingdetail(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/pendingdetail(-|/)(\d+)(\.html|/?|)$ /index.php?mod=pending_detail&id=$2 last;
}

# 拒绝详细页面 - 使用id参数，支持伪静态URL
if ($uri ~ "^/rejecteddetail(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/rejecteddetail(-|/)(\d+)(\.html|/?|)$ /index.php?mod=rejected_detail&id=$2 last;
}

# RSS订阅 - 支持模块和分类
if ($uri ~ "^/rssfeed(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/rssfeed(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=rssfeed&type=$2&cid=$4 last;
}
if ($uri ~ "^/rssfeed(-|/)(.+?)(\.html|/?|)$") {
    rewrite ^/rssfeed(-|/)(.+?)(\.html|/?|)$ /index.php?mod=rssfeed&type=$2 last;
}

# 网站地图 - 支持模块和分类
if ($uri ~ "^/sitemap(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/sitemap(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=sitemap&type=$2&cid=$4 last;
}
if ($uri ~ "^/sitemap(-|/)(.+?)(\.html|/?|)$") {
    rewrite ^/sitemap(-|/)(.+?)(\.html|/?|)$ /index.php?mod=sitemap&cid=$2 last;
}

# 分类目录 - 支持网站目录和文章分类（三种URL结构）
# 结构1: webdir-分类目录-分类ID-页码.html
if ($uri ~ "^/(webdir|article)(-|/)(.+?)(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/(webdir|article)(-|/)(.+?)(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=$1&cid=$5&page=$7 last;
}
# 结构2: webdir/分类目录/分类ID-页码.html
if ($uri ~ "^/(webdir|article)/(.+?)/(\d+)-(\d+)(\.html|/?|)$") {
    rewrite ^/(webdir|article)/(.+?)/(\d+)-(\d+)(\.html|/?|)$ /index.php?mod=$1&cid=$3&page=$4 last;
}
# 结构3: webdir/分类目录/分类ID/页码
if ($uri ~ "^/(webdir|article)/(.+?)/(\d+)/(\d+)/?$") {
    rewrite ^/(webdir|article)/(.+?)/(\d+)/(\d+)/?$ /index.php?mod=$1&cid=$3&page=$4 last;
}

# 分类目录首页（无分页）
if ($uri ~ "^/(webdir|article)(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/(webdir|article)(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=$1&cid=$5 last;
}

# AJAX和API接口
if ($uri ~ "^/(ajaxget|getdata|api)(\.php|/?|)$") {
    rewrite ^/(ajaxget|getdata|api)(\.php|/?|)$ /index.php?mod=$1 last;
}

# 兼容旧版本URL结构
if ($uri ~ "^/(siteinfo|artinfo|linkinfo)(-|/)(\d+)(-|/)(.+?)(\.html|/?|)$") {
    rewrite ^/(siteinfo|artinfo|linkinfo)(-|/)(\d+)(-|/)(.+?)(\.html|/?|)$ /index.php?mod=$1&wid=$3 last;
}

# 处理不存在的文件，转发到index.php
if (!-e $request_filename) {
    rewrite ^ /index.php last;
}

# ========================================
# URL示例说明
# ========================================
#
# 基础模块访问：
# https://www.95dir.com/index -> ?mod=index
# https://www.95dir.com/webdir -> ?mod=webdir
# https://www.95dir.com/addurl -> ?mod=addurl
# https://www.95dir.com/datastats -> ?mod=datastats
#
# VIP网站相关：
# https://www.95dir.com/vip/ -> ?mod=vip_list
# https://www.95dir.com/vip/list/ -> ?mod=vip_list
# https://www.95dir.com/vip/list/2/ -> ?mod=vip_list&page=2
# https://www.95dir.com/vip/category/1/ -> ?mod=vip_list&cid=1
# https://www.95dir.com/vip/category/1/2/ -> ?mod=vip_list&cid=1&page=2
# https://www.95dir.com/vip/detail/481/ -> ?mod=vip_detail&id=481
# https://www.95dir.com/vipdetail/481.html -> ?mod=vip_detail&id=481
#
# 黑名单网站相关：
# https://www.95dir.com/blacklist/ -> ?mod=blacklist
# https://www.95dir.com/blacklist/2/ -> ?mod=blacklist&page=2
# https://www.95dir.com/blacklist/category/1/ -> ?mod=blacklist&category=1
# https://www.95dir.com/blacklist/category/1/2/ -> ?mod=blacklist&category=1&page=2
# https://www.95dir.com/blacklist/detail/481/ -> ?mod=blacklist_detail&id=481
# https://www.95dir.com/blacklistdetail/481.html -> ?mod=blacklist_detail&id=481
#
# 待审核网站相关：
# https://www.95dir.com/pending/ -> ?mod=pending
# https://www.95dir.com/pending/2/ -> ?mod=pending&page=2
# https://www.95dir.com/pending/category/1/ -> ?mod=pending&cid=1
# https://www.95dir.com/pending/category/1/2/ -> ?mod=pending&cid=1&page=2
# https://www.95dir.com/pending/detail/481/ -> ?mod=pending_detail&id=481
# https://www.95dir.com/pendingdetail/481.html -> ?mod=pending_detail&id=481