# ========================================
# 95分类目录 Nginx 伪静态规则
# 适用于 95DIR-v3.0 系统
# ========================================

# 基础模块（首页、分类浏览、数据归档、最近更新、排行榜、意见反馈等）
if ($uri ~ "^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|blacklist|pending|rejected|addurl|quicksubmit|datastats|vip_list)(\.html|/?|)$") {
    rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|blacklist|pending|rejected|addurl|quicksubmit|datastats|vip_list)(\.html|/?|)$ /index.php?mod=$1 last;
}

# 最近更新模块 - 支持天数和分页
if ($uri ~ "^/update(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/update(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=update&days=$2&page=$4 last;
}
if ($uri ~ "^/update(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/update(-|/)(\d+)(\.html|/?|)$ /index.php?mod=update&days=$2 last;
}

# 数据归档模块 - 支持日期和分页
if ($uri ~ "^/archives(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/archives(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=archives&date=$2&page=$4 last;
}
if ($uri ~ "^/archives(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/archives(-|/)(\d+)(\.html|/?|)$ /index.php?mod=archives&date=$2 last;
}

# 站内搜索 - 支持多种搜索类型
if ($uri ~ "^/search(-|/)(name|url|tags|intro|baidu|br|pr|sr|article)(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/search(-|/)(name|url|tags|intro|baidu|br|pr|sr|article)(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=search&type=$2&query=$4&page=$6 last;
}
if ($uri ~ "^/search(-|/)(name|url|tags|intro|baidu|br|pr|sr|article)(-|/)(.+?)(\.html|/?|)$") {
    rewrite ^/search(-|/)(name|url|tags|intro|baidu|br|pr|sr|article)(-|/)(.+?)(\.html|/?|)$ /index.php?mod=search&type=$2&query=$4 last;
}

# 详细页面 - 网站详细、文章详细、链接详细、自定义页面
if ($uri ~ "^/(siteinfo|artinfo|linkinfo|diypage)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/(siteinfo|artinfo|linkinfo|diypage)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=$1&wid=$3 last;
}

# 待审核详细页面
if ($uri ~ "^/(pending_detail|blacklist_detail|rejected_detail)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/(pending_detail|blacklist_detail|rejected_detail)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=$1&wid=$3 last;
}

# VIP详细页面
if ($uri ~ "^/vip_detail(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/vip_detail(-|/)(\d+)(\.html|/?|)$ /index.php?mod=vip_detail&wid=$2 last;
}

# RSS订阅 - 支持模块和分类
if ($uri ~ "^/rssfeed(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/rssfeed(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=rssfeed&type=$2&cid=$4 last;
}
if ($uri ~ "^/rssfeed(-|/)(.+?)(\.html|/?|)$") {
    rewrite ^/rssfeed(-|/)(.+?)(\.html|/?|)$ /index.php?mod=rssfeed&type=$2 last;
}

# 网站地图 - 支持模块和分类
if ($uri ~ "^/sitemap(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/sitemap(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=sitemap&type=$2&cid=$4 last;
}
if ($uri ~ "^/sitemap(-|/)(.+?)(\.html|/?|)$") {
    rewrite ^/sitemap(-|/)(.+?)(\.html|/?|)$ /index.php?mod=sitemap&cid=$2 last;
}

# 分类目录 - 支持网站目录和文章分类（三种URL结构）
# 结构1: webdir-分类目录-分类ID-页码.html
if ($uri ~ "^/(webdir|article)(-|/)(.+?)(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/(webdir|article)(-|/)(.+?)(-|/)(\d+)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=$1&cid=$5&page=$7 last;
}
# 结构2: webdir/分类目录/分类ID-页码.html
if ($uri ~ "^/(webdir|article)/(.+?)/(\d+)-(\d+)(\.html|/?|)$") {
    rewrite ^/(webdir|article)/(.+?)/(\d+)-(\d+)(\.html|/?|)$ /index.php?mod=$1&cid=$3&page=$4 last;
}
# 结构3: webdir/分类目录/分类ID/页码
if ($uri ~ "^/(webdir|article)/(.+?)/(\d+)/(\d+)/?$") {
    rewrite ^/(webdir|article)/(.+?)/(\d+)/(\d+)/?$ /index.php?mod=$1&cid=$3&page=$4 last;
}

# 分类目录首页（无分页）
if ($uri ~ "^/(webdir|article)(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$") {
    rewrite ^/(webdir|article)(-|/)(.+?)(-|/)(\d+)(\.html|/?|)$ /index.php?mod=$1&cid=$5 last;
}

# AJAX和API接口
if ($uri ~ "^/(ajaxget|getdata|api)(\.php|/?|)$") {
    rewrite ^/(ajaxget|getdata|api)(\.php|/?|)$ /index.php?mod=$1 last;
}

# 兼容旧版本URL结构
if ($uri ~ "^/(siteinfo|artinfo|linkinfo)(-|/)(\d+)(-|/)(.+?)(\.html|/?|)$") {
    rewrite ^/(siteinfo|artinfo|linkinfo)(-|/)(\d+)(-|/)(.+?)(\.html|/?|)$ /index.php?mod=$1&wid=$3 last;
}

# 处理不存在的文件，转发到index.php
if (!-e $request_filename) {
    rewrite ^ /index.php last;
}