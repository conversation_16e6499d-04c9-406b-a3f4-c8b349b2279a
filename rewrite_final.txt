# ===== 最终版伪静态规则 =====
# 解决 https://www.95dir.com/webdir/?mod=vip_list 无法转换的问题

# 基础模块（包含所有新增模块）
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|blacklist|pending|rejected|addurl|quicksubmit|datastats|vip_list|vip_detail|pending_detail|blacklist_detail|rejected_detail)(/?)$ /index.php?mod=$1;

# 最近更新模块
rewrite ^/update/(\d+)\.html$ /index.php?mod=update&days=$1;
rewrite ^/update/(\d+)-(\d+)\.html$ /index.php?mod=update&days=$1&page=$2;

# 数据归档模块
rewrite ^/archives/(\d+)\.html$ /index.php?mod=archives&date=$1;
rewrite ^/archives/(\d+)-(\d+)\.html$ /index.php?mod=archives&date=$1&page=$2;

# 站内搜索模块
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 快速搜索
rewrite ^/(br|pr)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/(br|pr)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 网站详情页
rewrite ^/view/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo-(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/site/(\d+)-(.+)(/?)\.html$ /index.php?mod=siteinfo&wid=$1;

# 文章详情页
rewrite ^/artinfo/(\d+)\.html$ /index.php?mod=artinfo&aid=$1;

# 友情链接详情页
rewrite ^/linkinfo/(\d+)\.html$ /index.php?mod=linkinfo&lid=$1;

# 自定义单页
rewrite ^/diypage/(\d+)\.html$ /index.php?mod=diypage&pid=$1;

# RSS订阅
rewrite ^/rssfeed/(\d+)\.html$ /index.php?mod=rssfeed&cid=$1;

# 网站地图
rewrite ^/sitemap/(\d+)\.html$ /index.php?mod=sitemap&cid=$1;

# 网站目录分类页面
rewrite ^/webdir/(.+)/(\d+)\.html$ /index.php?mod=webdir&cid=$2;
rewrite ^/webdir/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3;
rewrite ^/webdir/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&sort=$3&page=$4;

# 友情链接分类页面
rewrite ^/weblink/(.+)/(\d+)\.html$ /index.php?mod=weblink&cid=$2;
rewrite ^/weblink/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3;
rewrite ^/weblink/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&sort=$3&page=$4;

# 文章分类页面
rewrite ^/article/(.+)/(\d+)\.html$ /index.php?mod=article&cid=$2;
rewrite ^/article/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3;

# RSS订阅分类页面
rewrite ^/rssfeed/(.+)/(\d+)\.html$ /index.php?mod=rssfeed&cid=$2;
rewrite ^/rssfeed/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rssfeed&cid=$2&page=$3;

# ===== VIP网站相关 =====
rewrite ^/vip/(.+)/(\d+)\.html$ /index.php?mod=vip_list&cid=$2;
rewrite ^/vip/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=vip_list&cid=$2&page=$3;
rewrite ^/vipdetail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1;

# ===== 待审核网站相关 =====
rewrite ^/pending/(.+)/(\d+)\.html$ /index.php?mod=pending&cid=$2;
rewrite ^/pending/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=pending&cid=$2&page=$3;
rewrite ^/pendingdetail/(\d+)\.html$ /index.php?mod=pending_detail&id=$1;

# ===== 黑名单网站相关 =====
rewrite ^/blacklist/(.+)/(\d+)\.html$ /index.php?mod=blacklist&category=$2;
rewrite ^/blacklist/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=blacklist&category=$2&page=$3;
rewrite ^/blacklistdetail/(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1;

# ===== 审核不通过网站相关 =====
rewrite ^/rejected/(.+)/(\d+)\.html$ /index.php?mod=rejected&cid=$2;
rewrite ^/rejected/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rejected&cid=$2&page=$3;
rewrite ^/rejecteddetail/(\d+)\.html$ /index.php?mod=rejected_detail&id=$1;

# 网站图标获取
rewrite ^/ico/(.*)\.png$ /ico/get.php?url=$1;

# 网站快照
rewrite ^/snapshot(-|\/)(.*)$ /snapshot.php?site=$2;

# ===== 测试URL说明 =====
# 现在这些URL都应该可以正常工作：

# 基础页面：
# https://www.95dir.com/vip_list -> ?mod=vip_list
# https://www.95dir.com/vip_list/ -> ?mod=vip_list
# https://www.95dir.com/pending -> ?mod=pending
# https://www.95dir.com/blacklist -> ?mod=blacklist
# https://www.95dir.com/rejected -> ?mod=rejected

# VIP相关：
# https://www.95dir.com/vip/科技/1.html -> ?mod=vip_list&cid=1
# https://www.95dir.com/vip/科技/1-2.html -> ?mod=vip_list&cid=1&page=2
# https://www.95dir.com/vipdetail/123.html -> ?mod=vip_detail&id=123

# 待审核相关：
# https://www.95dir.com/pending/科技/1.html -> ?mod=pending&cid=1
# https://www.95dir.com/pendingdetail/123.html -> ?mod=pending_detail&id=123

# 黑名单相关：
# https://www.95dir.com/blacklist/违规/1.html -> ?mod=blacklist&category=1
# https://www.95dir.com/blacklistdetail/123.html -> ?mod=blacklist_detail&id=123

# 审核不通过相关：
# https://www.95dir.com/rejected/科技/1.html -> ?mod=rejected&cid=1
# https://www.95dir.com/rejecteddetail/123.html -> ?mod=rejected_detail&id=123

# ===== 重要提示 =====
# 1. 将这些规则添加到您的Nginx配置文件中
# 2. 重新加载Nginx配置：nginx -s reload
# 3. 清除浏览器缓存和程序缓存
# 4. 测试URL：https://www.95dir.com/vip_list 应该可以正常访问

# ===== 如果还是不工作，请检查 =====
# 1. Nginx配置文件是否正确加载了这些规则
# 2. 是否有其他规则冲突
# 3. 程序的缓存是否已清除
# 4. 可以先测试简单的规则，如：https://www.95dir.com/vip_list
