<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '黑名单网站详情';
$pageurl = '?mod=blacklist_detail';
$tempfile = 'blacklist_detail.html';

// 获取黑名单分类（与列表页保持一致）
function get_blacklist_categories() {
    $categories = array();
    $categories[0] = '其他';
    $categories[1] = '违法违规';
    $categories[2] = '色情内容';
    $categories[3] = '赌博博彩';
    $categories[4] = '诈骗欺诈';
    $categories[5] = '恶意软件';
    $categories[6] = '垃圾信息';
    $categories[7] = '版权侵权';
    $categories[8] = '政治敏感';

    return $categories;
}

$web_id = intval($_GET['id']);
$cache_id = $web_id;

if (!$web_id) {
    redirect('?mod=blacklist');
}

if (!$smarty->isCached($tempfile, $cache_id)) {
	// 查询黑名单网站，关联webdata表获取更新时间
	$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_tags, w.web_ctime, w.cate_id, w.user_id, w.web_pic,
	               w.web_blacklist_reason, w.web_blacklist_category, w.web_blacklist_time, w.web_blacklist_operator,
	               d.web_utime
	        FROM " . $DB->table('websites') . " w
	        LEFT JOIN " . $DB->table('webdata') . " d ON w.web_id = d.web_id
	        WHERE w.web_status = 1 AND w.web_id = $web_id
	        LIMIT 1";

	$web = $DB->fetch_one($sql);
	if (!$web) {
		redirect('?mod=blacklist');
	}

	$cate = get_one_category($web['cate_id']);
	$user = get_one_user($web['user_id']);

	$smarty->assign('site_title', $web['web_name'].' - '.$pagename.' - '.$options['site_name']);
	$smarty->assign('site_keywords', !empty($web['web_tags']) ? $web['web_tags'] : $options['site_keywords']);
	$smarty->assign('site_description', !empty($web['web_intro']) ? mb_substr(strip_tags($web['web_intro']), 0, 200, 'utf-8') : $options['site_description']);
	$smarty->assign('site_path', get_sitepath().' &raquo; <a href="?mod=blacklist">黑名单</a> &raquo; <a href="?mod=blacklist&cid='.$web['cate_id'].'">'.$cate['cate_name'].'</a> &raquo; '.$web['web_name']);
	$smarty->assign('site_rss', get_rssfeed());

	// 获取黑名单分类
	$categories = get_blacklist_categories();

	// 处理黑名单数据（与列表页保持一致）
	if (empty($web['web_blacklist_category'])) {
		$web['web_blacklist_category'] = 0;
	}
	if (empty($web['web_blacklist_reason'])) {
		$web['web_blacklist_reason'] = '该网站已被列入黑名单';
	}
	if (empty($web['web_blacklist_time'])) {
		$web['web_blacklist_time'] = $web['web_ctime'];
	}
	if (empty($web['web_blacklist_operator'])) {
		$web['web_blacklist_operator'] = 'system';
	}

	$web['category_name'] = $categories[$web['web_blacklist_category']];
	$web['blacklist_time_formatted'] = $web['web_blacklist_time'] ? date('Y-m-d', is_numeric($web['web_blacklist_time']) ? $web['web_blacklist_time'] : strtotime($web['web_blacklist_time'])) : '';
	$web['ctime_formatted'] = date('Y-m-d', $web['web_ctime']);

	// 隐藏网站URL，只显示域名
	$web['domain'] = parse_url('http://' . $web['web_url'], PHP_URL_HOST);
	if (!$web['domain']) {
		$web['domain'] = $web['web_url'];
	}

	// 隐私保护：网址信息设为隐藏状态
	$web['web_url_display'] = '隐私保护';
	$web['web_furl'] = '隐私保护';

	$web['web_pic'] = get_webthumb($web['web_pic']);
	$web['web_ip'] = '隐私保护'; // 隐藏IP信息
	$web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
	$web['web_utime'] = $web['web_utime'] ? date('Y-m-d', $web['web_utime']) : date('Y-m-d', $web['web_ctime']);

	// PageRank处理 - 黑名单状态显示默认值
	$web['web_prank'] = 0;
	$web['web_grank'] = 0;
	$web['web_brank'] = 0;
	$web['web_srank'] = 0;
	$web['web_arank'] = 0;
	$web['web_views'] = 0; // 黑名单状态浏览量为0
	$web['web_instat'] = 0; // 入站次数为0
	$web['web_outstat'] = 0; // 出站次数为0

	$smarty->assign('cate_id', $cate['cate_id']);
	$smarty->assign('cate_name', $cate['cate_name']);
	$smarty->assign('cate_keywords', $cate['cate_keywords']);
	$smarty->assign('cate_description', $cate['cate_description']);
	$smarty->assign('categories', $categories);

	/** tags */
	$web_tags = get_format_tags($web['web_tags']);
	$smarty->assign('web_tags', $web_tags);

	// 获取同分类的其他黑名单网站
	$related_blacklist = $DB->fetch_all("SELECT web_id, web_name, web_pic, web_url FROM ".$DB->table('websites')."
	                                  WHERE cate_id=".$web['cate_id']." AND web_status=1 AND web_id!=$web_id
	                                  ORDER BY web_ctime DESC LIMIT 8");

	// 处理相关黑名单网站的图片和链接
	foreach ($related_blacklist as &$rel) {
		$rel['web_pic'] = get_webthumb($rel['web_pic']);
		// 使用伪静态URL
		$rel['web_link'] = $options['site_root'] . 'blacklistdetail/' . $rel['web_id'] . '.html';
	}

    $smarty->assign('web', $web);
	$smarty->assign('user', $user);
	$smarty->assign('related_blacklist', $related_blacklist);

	// 获取上一站下一站（同分类的黑名单网站）
	$prev_blacklist = $DB->fetch_one("SELECT web_id, web_name FROM ".$DB->table('websites')."
	                               WHERE cate_id=".$web['cate_id']." AND web_status=1 AND web_id<$web_id
	                               ORDER BY web_id DESC LIMIT 1");
	$next_blacklist = $DB->fetch_one("SELECT web_id, web_name FROM ".$DB->table('websites')."
	                               WHERE cate_id=".$web['cate_id']." AND web_status=1 AND web_id>$web_id
	                               ORDER BY web_id ASC LIMIT 1");

	// 为上一站下一站添加链接 - 使用伪静态URL
	if ($prev_blacklist) {
		$prev_blacklist['web_link'] = $options['site_root'] . 'blacklistdetail/' . $prev_blacklist['web_id'] . '.html';
	}
	if ($next_blacklist) {
		$next_blacklist['web_link'] = $options['site_root'] . 'blacklistdetail/' . $next_blacklist['web_id'] . '.html';
	}

	$smarty->assign('prev_website', $prev_blacklist);
	$smarty->assign('next_website', $next_blacklist);

	// 相关站点显示同分类的正常网站
	$smarty->assign('related_website', get_websites($web['cate_id'], 10, false, false, 'ctime'));
}

smarty_output($tempfile, $cache_id);
?>
